{
  "nbformat": 4,
  "nbformat_minor": 0,
  "metadata": {
    "colab": {
      "provenance": []
    },
    "kernelspec": {
      "name": "python3",
      "display_name": "Python 3"
    },
    "language_info": {
      "name": "python"
    }
  },
  "cells": [
    {
      "cell_type": "markdown",
      "source": [
        "# Vietnamese TTS Training Pipeline - FIXED VERSION\n",
        "# FastSpeech2 + HiFi-GAN for Vietnamese Text-to-Speech\n",
        "# Compatible with Google Colab - Fixes formatter and import issues\n"
      ],
      "metadata": {
        "id": "title_cell"
      }
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 1. INSTALLATION AND SETUP\n",
        "# ================================\n",
        "\n",
        "# Install specific versions for compatibility\n",
        "!pip install -q torch torchvision torchaudio\n",
        "!pip install -q coqui-tts\n",
        "!pip install -q librosa soundfile\n",
        "!pip install -q matplotlib\n",
        "!pip install -q pandas\n",
        "!pip install -q unidecode\n",
        "!pip install -q tensorboard\n",
        "\n",
        "# Check TTS version\n",
        "import TTS\n",
        "print(f\"TTS version: {TTS.__version__}\")"
      ],
      "metadata": {
        "id": "install_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "import os\n",
        "import shutil\n",
        "import json\n",
        "import pandas as pd\n",
        "import numpy as np\n",
        "import torch\n",
        "import librosa\n",
        "import soundfile as sf\n",
        "from pathlib import Path\n",
        "import matplotlib.pyplot as plt\n",
        "from google.colab import drive, files\n",
        "import zipfile\n",
        "import re\n",
        "import subprocess\n",
        "import unicodedata"
      ],
      "metadata": {
        "id": "imports_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# Mount Google Drive for persistent storage\n",
        "drive.mount('/content/drive')\n",
        "\n",
        "# Create working directories\n",
        "os.makedirs('/content/tts_training', exist_ok=True)\n",
        "os.makedirs('/content/tts_training/wavs', exist_ok=True)\n",
        "os.makedirs('/content/tts_training/checkpoints', exist_ok=True)\n",
        "os.makedirs('/content/drive/MyDrive/TTS_Checkpoints', exist_ok=True)\n",
        "\n",
        "print(\"✅ Setup complete!\")"
      ],
      "metadata": {
        "id": "setup_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 2. VIETNAMESE TEXT CLEANER\n",
        "# ================================\n",
        "\n",
        "class VietnameseTextCleaner:\n",
        "    \"\"\"\n",
        "    Vietnamese text cleaner that preserves punctuation for natural speech\n",
        "    \"\"\"\n",
        "    def __init__(self):\n",
        "        # Vietnamese alphabet with tones\n",
        "        self.vietnamese_chars = set(\n",
        "            'aàáạảãâầấậẩẫăằắặẳẵeèéẹẻẽêềếệểễiìíịỉĩoòóọỏõôồốộổỗơờớợởỡuùúụủũưừứựửữyỳýỵỷỹ'\n",
        "            'AÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴEÈÉẸẺẼÊỀẾỆỂỄIÌÍỊỈĨOÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠUÙÚỤỦŨƯỪỨỰỬỮYỲÝỴỶỸ'\n",
        "            'bcdđfghklmnpqrstvwxzBCDĐFGHKLMNPQRSTVWXZ'\n",
        "        )\n",
        "\n",
        "        # Punctuation to preserve for natural speech\n",
        "        self.keep_punctuation = set('.,;:!?()[]{}\"\"''…-–—')\n",
        "\n",
        "        # Number to word mapping for Vietnamese\n",
        "        self.ones = ['', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín']\n",
        "        self.tens = ['', '', 'hai mươi', 'ba mươi', 'bốn mươi', 'năm mươi', \n",
        "                    'sáu mươi', 'bảy mươi', 'tám mươi', 'chín mươi']\n",
        "        self.teens = ['mười', 'mười một', 'mười hai', 'mười ba', 'mười bốn', \n",
        "                     'mười năm', 'mười sáu', 'mười bảy', 'mười tám', 'mười chín']\n",
        "\n",
        "    def number_to_vietnamese(self, num):\n",
        "        \"\"\"Convert a number to Vietnamese words\"\"\"\n",
        "        if num == 0:\n",
        "            return 'không'\n",
        "        \n",
        "        if num < 0:\n",
        "            return 'âm ' + self.number_to_vietnamese(-num)\n",
        "        \n",
        "        if num < 10:\n",
        "            return self.ones[num]\n",
        "        \n",
        "        if num < 20:\n",
        "            return self.teens[num - 10]\n",
        "        \n",
        "        if num < 100:\n",
        "            tens_digit = num // 10\n",
        "            ones_digit = num % 10\n",
        "            if ones_digit == 0:\n",
        "                return self.tens[tens_digit]\n",
        "            elif ones_digit == 1 and tens_digit > 1:\n",
        "                return self.tens[tens_digit] + ' mốt'\n",
        "            elif ones_digit == 5 and tens_digit > 1:\n",
        "                return self.tens[tens_digit] + ' lăm'\n",
        "            else:\n",
        "                return self.tens[tens_digit] + ' ' + self.ones[ones_digit]\n",
        "        \n",
        "        if num < 1000:\n",
        "            hundreds = num // 100\n",
        "            remainder = num % 100\n",
        "            result = self.ones[hundreds] + ' trăm'\n",
        "            if remainder > 0:\n",
        "                if remainder < 10:\n",
        "                    result += ' lẻ ' + self.ones[remainder]\n",
        "                else:\n",
        "                    result += ' ' + self.number_to_vietnamese(remainder)\n",
        "            return result\n",
        "        \n",
        "        if num < 1000000:\n",
        "            thousands = num // 1000\n",
        "            remainder = num % 1000\n",
        "            result = self.number_to_vietnamese(thousands) + ' nghìn'\n",
        "            if remainder > 0:\n",
        "                if remainder < 100:\n",
        "                    result += ' lẻ ' + self.number_to_vietnamese(remainder)\n",
        "                else:\n",
        "                    result += ' ' + self.number_to_vietnamese(remainder)\n",
        "            return result\n",
        "        \n",
        "        if num < 1000000000:\n",
        "            millions = num // 1000000\n",
        "            remainder = num % 1000000\n",
        "            result = self.number_to_vietnamese(millions) + ' triệu'\n",
        "            if remainder > 0:\n",
        "                result += ' ' + self.number_to_vietnamese(remainder)\n",
        "            return result\n",
        "        \n",
        "        # For very large numbers\n",
        "        return str(num)  # fallback to original number\n",
        "\n",
        "    def normalize_numbers(self, text):\n",
        "        \"\"\"Convert all numbers in text to Vietnamese words\"\"\"\n",
        "        import re\n",
        "        \n",
        "        # Find all numbers (including decimals and years)\n",
        "        def replace_number(match):\n",
        "            number_str = match.group()\n",
        "            \n",
        "            # Handle decimal numbers\n",
        "            if '.' in number_str or ',' in number_str:\n",
        "                # Replace decimal separator\n",
        "                number_str = number_str.replace(',', '.')\n",
        "                parts = number_str.split('.')\n",
        "                if len(parts) == 2:\n",
        "                    integer_part = int(parts[0]) if parts[0] else 0\n",
        "                    decimal_part = parts[1]\n",
        "                    \n",
        "                    result = self.number_to_vietnamese(integer_part)\n",
        "                    if decimal_part:\n",
        "                        result += ' phẩy '\n",
        "                        # Read each decimal digit\n",
        "                        for digit in decimal_part:\n",
        "                            if digit.isdigit():\n",
        "                                result += self.ones[int(digit)] + ' '\n",
        "                    return result.strip()\n",
        "            \n",
        "            # Handle regular integers\n",
        "            try:\n",
        "                num = int(number_str)\n",
        "                return self.number_to_vietnamese(num)\n",
        "            except ValueError:\n",
        "                return number_str\n",
        "        \n",
        "        # Replace all numbers with Vietnamese words\n",
        "        # Pattern matches integers and decimals\n",
        "        pattern = r'\\b\\d+(?:[.,]\\d+)?\\b'\n",
        "        text = re.sub(pattern, replace_number, text)\n",
        "        \n",
        "        return text\n",
        "\n",
        "    def clean_text(self, text):\n",
        "        \"\"\"Clean text while preserving Vietnamese characters and important punctuation\"\"\"\n",
        "        # Normalize unicode\n",
        "        text = unicodedata.normalize('NFC', text)\n",
        "\n",
        "        # Remove extra whitespace\n",
        "        text = re.sub(r'\\s+', ' ', text)\n",
        "\n",
        "        # Keep only Vietnamese characters, numbers, and important punctuation\n",
        "        cleaned_chars = []\n",
        "        for char in text:\n",
        "            if (char in self.vietnamese_chars or\n",
        "                char.isdigit() or\n",
        "                char in self.keep_punctuation or\n",
        "                char.isspace()):\n",
        "                cleaned_chars.append(char)\n",
        "\n",
        "        cleaned_text = ''.join(cleaned_chars)\n",
        "\n",
        "        # Convert numbers to words\n",
        "        cleaned_text = self.normalize_numbers(cleaned_text)\n",
        "\n",
        "        # Final cleanup\n",
        "        cleaned_text = re.sub(r'\\s+', ' ', cleaned_text).strip()\n",
        "\n",
        "        return cleaned_text\n",
        "\n",
        "# Initialize the cleaner\n",
        "text_cleaner = VietnameseTextCleaner()\n",
        "\n",
        "# Test the improved cleaner\n",
        "test_texts = [\n",
        "    \"Khi đó là mùa thu tháng 10 năm 2000.\",\n",
        "    \"Giá sản phẩm là 1.500.000 đồng.\",\n",
        "    \"Nhiệt độ hôm nay là 25.5 độ C.\",\n",
        "    \"Tôi sinh năm 1995 và hiện tại 28 tuổi.\",\n",
        "    \"Số điện thoại: 0123456789\",\n",
        "    \"Địa chỉ: số 123 đường ABC, quận 1\"\n",
        "]\n",
        "\n",
        "print(\"🧪 Testing improved Vietnamese text cleaner:\")\n",
        "print(\"=\" * 60)\n",
        "for test_text in test_texts:\n",
        "    cleaned = text_cleaner.clean_text(test_text)\n",
        "    print(f\"Original: {test_text}\")\n",
        "    print(f\"Cleaned:  {cleaned}\")\n",
        "    print(\"-\" * 60)"
      ],
      "metadata": {
        "id": "text_cleaner_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 3. CUSTOM VIETNAMESE FORMATTER\n",
        "# ================================\n",
        "\n",
        "def vietnamese_formatter(root_path, meta_file, **kwargs):\n",
        "    \"\"\"Custom formatter for Vietnamese TTS dataset\"\"\"\n",
        "    import os\n",
        "    \n",
        "    txt_file = os.path.join(root_path, meta_file)\n",
        "    items = []\n",
        "    \n",
        "    print(f\"📁 Loading data from: {txt_file}\")\n",
        "    \n",
        "    if not os.path.exists(txt_file):\n",
        "        print(f\"❌ Metadata file not found: {txt_file}\")\n",
        "        return items\n",
        "    \n",
        "    with open(txt_file, \"r\", encoding=\"utf-8\") as ttf:\n",
        "        for line_num, line in enumerate(ttf, 1):\n",
        "            line = line.strip()\n",
        "            if line and '|' in line:\n",
        "                parts = line.split('|')\n",
        "                if len(parts) >= 2:\n",
        "                    wav_file = parts[0].strip()\n",
        "                    text = parts[1].strip()\n",
        "                    \n",
        "                    # Ensure wav file has .wav extension\n",
        "                    if not wav_file.endswith('.wav'):\n",
        "                        wav_file += '.wav'\n",
        "                    \n",
        "                    # Full path to audio file\n",
        "                    audio_file = os.path.join(root_path, \"wavs\", wav_file)\n",
        "                    \n",
        "                    # Check if audio file exists\n",
        "                    if os.path.exists(audio_file):\n",
        "                        # Clean text\n",
        "                        cleaned_text = text_cleaner.clean_text(text)\n",
        "                        \n",
        "                        if len(cleaned_text.strip()) >= 3:  # Minimum text length\n",
        "                            items.append({\n",
        "                                \"text\": cleaned_text,\n",
        "                                \"audio_file\": audio_file,\n",
        "                                \"speaker_name\": \"vietnamese_speaker\",\n",
        "                                \"root_path\": root_path,\n",
        "                            })\n",
        "                        else:\n",
        "                            print(f\"⚠️  Skipping line {line_num}: text too short\")\n",
        "                    else:\n",
        "                        print(f\"⚠️  Audio file not found: {audio_file}\")\n",
        "                else:\n",
        "                    print(f\"⚠️  Invalid format on line {line_num}: {line[:50]}...\")\n",
        "    \n",
        "    print(f\"✅ Loaded {len(items)} valid samples\")\n",
        "    return items\n",
        "\n",
        "print(\"✅ Vietnamese formatter defined\")"
      ],
      "metadata": {
        "id": "formatter_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 4. DATA UPLOAD AND PREPARATION\n",
        "# ================================\n",
        "\n",
        "print(\"📤 Please upload your data:\")\n",
        "print(\"1. Upload your wavs/ folder as a ZIP file\")\n",
        "print(\"2. Upload your metadata.txt file\")\n",
        "print(\"\\nExpected metadata format:\")\n",
        "print(\"filename|transcript\")\n",
        "print(\"or\")\n",
        "print(\"filename|raw_text|normalized_text\")\n",
        "\n",
        "# Upload files\n",
        "uploaded = files.upload()\n",
        "\n",
        "# Process uploaded files\n",
        "for filename in uploaded.keys():\n",
        "    if filename.endswith('.zip'):\n",
        "        print(f\"📦 Extracting {filename}...\")\n",
        "        with zipfile.ZipFile(filename, 'r') as zip_ref:\n",
        "            zip_ref.extractall('/content/tts_training/')\n",
        "        print(\"✅ Audio files extracted\")\n",
        "    elif filename == 'metadata.txt':\n",
        "        shutil.move(filename, '/content/tts_training/metadata.txt')\n",
        "        print(\"✅ Metadata file moved\")\n",
        "\n",
        "# Check uploaded data\n",
        "wavs_dir = '/content/tts_training/wavs'\n",
        "metadata_path = '/content/tts_training/metadata.txt'\n",
        "\n",
        "print(\"\\n🔍 Checking uploaded files...\")\n",
        "print(f\"Metadata file exists: {os.path.exists(metadata_path)}\")\n",
        "print(f\"Wavs directory exists: {os.path.exists(wavs_dir)}\")\n",
        "\n",
        "if os.path.exists(wavs_dir):\n",
        "    wav_files = [f for f in os.listdir(wavs_dir) if f.endswith('.wav')]\n",
        "    print(f\"Found {len(wav_files)} WAV files\")\n",
        "    if len(wav_files) > 0:\n",
        "        print(f\"Sample files: {wav_files[:3]}\")\n",
        "\n",
        "if os.path.exists(metadata_path):\n",
        "    with open(metadata_path, 'r', encoding='utf-8') as f:\n",
        "        lines = f.readlines()[:5]\n",
        "    print(f\"\\nMetadata preview (first 5 lines):\")\n",
        "    for i, line in enumerate(lines, 1):\n",
        "        print(f\"  {i}: {line.strip()[:100]}...\")"
      ],
      "metadata": {
        "id": "upload_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 5. PREPARE DATASET FOR TRAINING\n",
        "# ================================\n",
        "\n",
        "def prepare_vietnamese_dataset(metadata_path, wavs_dir, output_dir):\n",
        "    \"\"\"Prepare Vietnamese dataset for TTS training\"\"\"\n",
        "    print(\"📁 Preparing Vietnamese dataset for training...\")\n",
        "    \n",
        "    if not os.path.exists(metadata_path):\n",
        "        print(f\"❌ Metadata file not found: {metadata_path}\")\n",
        "        return None, 0\n",
        "    \n",
        "    if not os.path.exists(wavs_dir):\n",
        "        print(f\"❌ Wavs directory not found: {wavs_dir}\")\n",
        "        return None, 0\n",
        "    \n",
        "    # Use the custom formatter to load data\n",
        "    samples = vietnamese_formatter(output_dir, 'metadata.txt')\n",
        "    \n",
        "    if len(samples) == 0:\n",
        "        print(\"❌ No valid samples found!\")\n",
        "        return None, 0\n",
        "    \n",
        "    # Create metadata.csv in the format expected by TTS\n",
        "    metadata_lines = []\n",
        "    for sample in samples:\n",
        "        # Extract filename without extension\n",
        "        audio_filename = os.path.basename(sample['audio_file'])\n",
        "        filename_no_ext = audio_filename.replace('.wav', '')\n",
        "        \n",
        "        # Format: filename|text\n",
        "        line = f\"{filename_no_ext}|{sample['text']}\"\n",
        "        metadata_lines.append(line)\n",
        "    \n",
        "    # Save metadata.csv\n",
        "    metadata_csv_path = os.path.join(output_dir, 'metadata.csv')\n",
        "    with open(metadata_csv_path, 'w', encoding='utf-8') as f:\n",
        "        for line in metadata_lines:\n",
        "            f.write(line + '\\n')\n",
        "    \n",
        "    print(f\"✅ Dataset prepared with {len(samples)} samples\")\n",
        "    print(f\"✅ Metadata saved to: {metadata_csv_path}\")\n",
        "    \n",
        "    # Show sample data\n",
        "    print(\"\\n📋 Sample data:\")\n",
        "    for i, sample in enumerate(samples[:3]):\n",
        "        filename = os.path.basename(sample['audio_file'])\n",
        "        print(f\"   {i+1}. {filename} -> {sample['text'][:50]}...\")\n",
        "    \n",
        "    return metadata_csv_path, len(samples)\n",
        "\n",
        "# Prepare the dataset\n",
        "if os.path.exists(metadata_path) and os.path.exists(wavs_dir):\n",
        "    metadata_csv, num_samples = prepare_vietnamese_dataset(\n",
        "        metadata_path, wavs_dir, '/content/tts_training'\n",
        "    )\n",
        "    if num_samples > 0:\n",
        "        print(f\"\\n🎯 Dataset ready for training with {num_samples} samples!\")\n",
        "    else:\n",
        "        print(\"\\n❌ Dataset preparation failed. Please check your data.\")\n",
        "else:\n",
        "    print(\"\\n❌ Please upload both wavs/ folder and metadata.txt file first.\")"
      ],
      "metadata": {
        "id": "prepare_dataset_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 6. TTS CONFIGURATION\n",
        "# ================================\n",
        "\n",
        "# Create TTS configuration for Vietnamese\n",
        "tts_config = {\n",
        "    \"model\": \"tacotron2\",\n",
        "    \"run_name\": \"vietnamese_tts_tacotron2\",\n",
        "    \"run_description\": \"Vietnamese TTS training with Tacotron2\",\n",
        "\n",
        "    # Audio settings\n",
        "    \"audio\": {\n",
        "        \"sample_rate\": 22050,\n",
        "        \"num_mels\": 80,\n",
        "        \"num_freq\": 1025,\n",
        "        \"frame_length_ms\": 50,\n",
        "        \"frame_shift_ms\": 12.5,\n",
        "        \"preemphasis\": 0.97,\n",
        "        \"min_level_db\": -100,\n",
        "        \"ref_level_db\": 20,\n",
        "        \"power\": 1.5,\n",
        "        \"griffin_lim_iters\": 60,\n",
        "        \"fft_size\": 1024,\n",
        "        \"win_length\": 1024,\n",
        "        \"hop_length\": 256,\n",
        "        \"mel_fmin\": 0,\n",
        "        \"mel_fmax\": 8000,\n",
        "        \"spec_gain\": 20,\n",
        "        \"signal_norm\": True,\n",
        "        \"symmetric_norm\": True,\n",
        "        \"max_norm\": 4.0,\n",
        "        \"clip_norm\": True,\n",
        "        \"do_trim_silence\": True,\n",
        "        \"trim_db\": 60\n",
        "    },\n",
        "\n",
        "    # Dataset settings - Use ljspeech format with custom data\n",
        "    \"dataset\": \"ljspeech\",\n",
        "    \"data_path\": \"/content/tts_training/\",\n",
        "    \"meta_file_train\": \"metadata.csv\",\n",
        "    \"meta_file_val\": \"metadata.csv\",\n",
        "    \"phoneme_cache_path\": \"/content/tts_training/phoneme_cache\",\n",
        "\n",
        "    # Training settings\n",
        "    \"batch_size\": 16,  # Reduced for stability\n",
        "    \"eval_batch_size\": 8,\n",
        "    \"num_loader_workers\": 2,\n",
        "    \"num_eval_loader_workers\": 1,\n",
        "    \"run_eval\": True,\n",
        "    \"test_delay_epochs\": 5,\n",
        "\n",
        "    # Optimizer\n",
        "    \"epochs\": 200,  # Reduced for faster training\n",
        "    \"lr\": 0.0001,\n",
        "    \"wd\": 0.000001,\n",
        "    \"grad_clip\": 1.0,\n",
        "    \"lr_scheduler\": \"ExponentialLR\",\n",
        "    \"lr_scheduler_params\": {\n",
        "        \"gamma\": 0.999875,\n",
        "        \"last_epoch\": -1\n",
        "    },\n",
        "\n",
        "    # Text processing for Vietnamese\n",
        "    \"text_cleaner\": \"basic_cleaners\",\n",
        "    \"enable_eos_bos_chars\": True,\n",
        "    \"add_blank\": True,\n",
        "    \"characters\": {\n",
        "        \"characters\": \"aàáạảãâầấậẩẫăằắặẳẵeèéẹẻẽêềếệểễiìíịỉĩoòóọỏõôồốộổỗơờớợởỡuùúụủũưừứựửữyỳýỵỷỹbcdđfghklmnpqrstvwxz .,;:!?()-\",\n",
        "        \"punctuations\": \".,;:!?()-\",\n",
        "        \"pad\": \"<PAD>\",\n",
        "        \"eos\": \"<EOS>\",\n",
        "        \"bos\": \"<BOS>\",\n",
        "        \"blank\": \"<BLANK>\"\n",
        "    },\n",
        "\n",
        "    # Checkpoint settings\n",
        "    \"output_path\": \"/content/tts_training/checkpoints/\",\n",
        "    \"save_step\": 500,\n",
        "    \"save_checkpoints\": True,\n",
        "    \"save_all_best\": True,\n",
        "    \"save_best_after\": 1000,\n",
        "    \"target_loss\": \"loss_1\",\n",
        "    \"print_step\": 25,\n",
        "    \"print_eval\": True,\n",
        "    \"mixed_precision\": False,\n",
        "\n",
        "    # Logging\n",
        "    \"dashboard_logger\": \"tensorboard\",\n",
        "    \"tb_log_dir\": \"/content/tts_training/logs/\",\n",
        "\n",
        "    # Evaluation\n",
        "    \"run_eval_steps\": 250,\n",
        "    \"test_sentences\": [\n",
        "        \"Xin chào, tôi là trợ lý ảo tiếng Việt.\",\n",
        "        \"Hôm nay trời đẹp, chúng ta đi chơi nhé!\",\n",
        "        \"Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi.\",\n",
        "        \"Chúc bạn một ngày tốt lành và hạnh phúc.\"\n",
        "    ]\n",
        "}\n",
        "\n",
        "# Save config\n",
        "config_path = '/content/tts_training/config.json'\n",
        "with open(config_path, 'w', encoding='utf-8') as f:\n",
        "    json.dump(tts_config, f, ensure_ascii=False, indent=2)\n",
        "\n",
        "print(f\"✅ Configuration saved to {config_path}\")"
      ],
      "metadata": {
        "id": "config_cell"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 7. PRE-TRAINING VALIDATION\n",
        "# ================================\n",
        "\n",
        "def validate_training_setup():\n",
        "    \"\"\"Validate that everything is ready for training\"\"\"\n",
        "    print(\"🔍 Validating training setup...\")\n",
        "    \n",
        "    issues = []\n",
        "    \n",
        "    # Check metadata file\n",
        "    metadata_file = '/content/tts_training/metadata.csv'\n",
        "    if not os.path.exists(metadata_file):\n",
        "        issues.append(\"❌ metadata.csv not found\")\n",
        "    else:\n",
        "        with open(metadata_file, 'r', encoding='utf-8') as f:\n",
        "            lines = f.readlines()\n",
        "        if len(lines) == 0:\n",
        "            issues.append(\"❌ metadata.csv is empty\")\n",
        "        else:\n",
        "            print(f\"✅ Found {len(lines)} entries in metadata.csv\")\n",
        "    \n",
        "    # Check wavs directory\n",
        "    wavs_dir = '/content/tts_training/wavs'\n",
        "    if not os.path.exists(wavs_dir):\n",
        "        issues.append(\"❌ wavs directory not found\")\n",
        "    else:\n",
        "        wav_files = [f for f in os.listdir(wavs_dir) if f.endswith('.wav')]\n",
        "        if len(wav_files) == 0:\n",
        "            issues.append(\"❌ No WAV files found in wavs directory\")\n",
        "        else:\n",
        "            print(f\"✅ Found {len(wav_files)} WAV files\")\n",
        "    \n",
        "    # Check config file\n",
        "    config_file = '/content/tts_training/config.json'\n",
        "    if not os.path.exists(config_file):\n",
        "        issues.append(\"❌ config.json not found\")\n",
        "    else:\n",
        "        print(\"✅ Configuration file found\")\n",
        "    \n",
        "    # Test custom formatter\n",
        "    try:\n",
        "        test_samples = vietnamese_formatter('/content/tts_training/', 'metadata.csv')\n",
        "        if len(test_samples) == 0:\n",
        "            issues.append(\"❌ Custom formatter returned no samples\")\n",
        "        else:\n",
        "            print(f\"✅ Custom formatter loaded {len(test_samples)} samples\")\n",
        "            # Show sample\n",
        "            if len(test_samples) > 0:\n",
        "                sample = test_samples[0]\n",
        "                print(f\"   Sample: {sample['text'][:50]}...\")\n",
        "    except Exception as e:\n",
        "        issues.append(f\"❌ Custom formatter error: {e}\")\n",
        "    \n",
        "    if issues:\n",
        "        print(\"\\n🚨 Issues found:\")\n",
        "        for issue in issues:\n",
        "            print(f\"  {issue}\")\n",
        "        return False\n",
        "    else:\n",
        "        print(\"\\n✅ All validation checks passed! Ready for training.\")\n",
        "        return True\n",
        "\n",
        "# Run validation\n",
        "validation_passed = validate_training_setup()"
      ],
      "metadata": {
        "id": \"validation_cell\"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 8. TRAINING FUNCTIONS\n",
        "# ================================\n",
        "\n",
        "def train_with_command_line(config_path, resume_path=None):\n",
        "    \"\"\"Train using command line approach - most reliable\"\"\"\n",
        "    print(\"🚀 Starting training with command line approach...\")\n",
        "    \n",
        "    # Prepare command\n",
        "    cmd = [\n",
        "        'python', '-m', 'TTS.bin.train_tts',\n",
        "        '--config_path', config_path,\n",
        "        '--output_path', '/content/tts_training/checkpoints/',\n",
        "    ]\n",
        "    \n",
        "    if resume_path and os.path.exists(resume_path):\n",
        "        cmd.extend(['--restore_path', resume_path])\n",
        "    \n",
        "    print(f\"🔧 Command: {' '.join(cmd)}\")\n",
        "    \n",
        "    try:\n",
        "        # Run training\n",
        "        process = subprocess.Popen(\n",
        "            cmd,\n",
        "            stdout=subprocess.PIPE,\n",
        "            stderr=subprocess.PIPE,\n",
        "            text=True,\n",
        "            bufsize=1,\n",
        "            universal_newlines=True\n",
        "        )\n",
        "        \n",
        "        print(\"📊 Training started! Monitor the output below:\")\n",
        "        print(\"\" + \"=\"*50)\n",
        "        \n",
        "        # Print output in real-time\n",
        "        while True:\n",
        "            output = process.stdout.readline()\n",
        "            if output == '' and process.poll() is not None:\n",
        "                break\n",
        "            if output:\n",
        "                print(output.strip())\n",
        "        \n",
        "        # Get any remaining output\n",
        "        stdout, stderr = process.communicate()\n",
        "        \n",
        "        if stdout:\n",
        "            print(stdout)\n",
        "        if stderr:\n",
        "            print(\"STDERR:\", stderr)\n",
        "        \n",
        "        return process.returncode == 0\n",
        "        \n",
        "    except Exception as e:\n",
        "        print(f\"❌ Training failed: {e}\")\n",
        "        return False\n",
        "\n",
        "def train_with_api_fallback():\n",
        "    \"\"\"Fallback training method using TTS API\"\"\"\n",
        "    print(\"🔄 Trying API-based training as fallback...\")\n",
        "    \n",
        "    try:\n",
        "        from TTS.api import TTS\n",
        "        \n",
        "        # Use a pre-trained model as base\n",
        "        print(\"📥 Loading pre-trained model...\")\n",
        "        tts = TTS(model_name=\"tts_models/en/ljspeech/tacotron2-DDC\", progress_bar=False)\n",
        "        \n",
        "        print(\"🔄 This will create a basic setup for manual training\")\n",
        "        print(\"💡 For full Vietnamese training, use the command line approach\")\n",
        "        \n",
        "        return True\n",
        "        \n",
        "    except Exception as e:\n",
        "        print(f\"❌ API fallback failed: {e}\")\n",
        "        return False\n",
        "\n",
        "print(\"✅ Training functions defined\")"
      ],
      "metadata": {
        "id": \"training_functions_cell\"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 9. START TRAINING\n",
        "# ================================\n",
        "\n",
        "if not validation_passed:\n",
        "    print(\"❌ Please fix validation issues before training\")\n",
        "    print(\"\\n🔧 Common fixes:\")\n",
        "    print(\"1. Make sure you uploaded both wavs.zip and metadata.txt\")\n",
        "    print(\"2. Check metadata.txt format: filename|transcript\")\n",
        "    print(\"3. Ensure WAV files exist in the wavs/ folder\")\n",
        "else:\n",
        "    print(\"🚀 Starting Vietnamese TTS training!\")\n",
        "    print(\"Configuration validated. Training will begin shortly.\")\n",
        "    \n",
        "    # Check if we want to resume from checkpoint\n",
        "    resume_from_checkpoint = input(\"Resume from checkpoint? (y/n): \").lower() == 'y'\n",
        "    \n",
        "    resume_path = None\n",
        "    if resume_from_checkpoint:\n",
        "        checkpoint_dir = '/content/tts_training/checkpoints'\n",
        "        if os.path.exists(checkpoint_dir):\n",
        "            checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]\n",
        "            if checkpoint_files:\n",
        "                print(\"Available checkpoints:\")\n",
        "                for i, cp in enumerate(checkpoint_files):\n",
        "                    print(f\"  {i+1}. {cp}\")\n",
        "                \n",
        "                try:\n",
        "                    choice = int(input(\"Select checkpoint (number): \")) - 1\n",
        "                    if 0 <= choice < len(checkpoint_files):\n",
        "                        resume_path = os.path.join(checkpoint_dir, checkpoint_files[choice])\n",
        "                        print(f\"✅ Will resume from: {checkpoint_files[choice]}\")\n",
        "                except:\n",
        "                    print(\"Invalid choice, starting fresh training\")\n",
        "            else:\n",
        "                print(\"No checkpoints found, starting fresh training\")\n",
        "    \n",
        "    # Start training\n",
        "    config_path = '/content/tts_training/config.json'\n",
        "    \n",
        "    print(\"\\n\" + \"=\"*50)\n",
        "    print(\"🎯 STARTING TRAINING\")\n",
        "    print(\"=\"*50)\n",
        "    \n",
        "    try:\n",
        "        success = train_with_command_line(config_path, resume_path)\n",
        "        \n",
        "        if success:\n",
        "            print(\"\\n🎉 Training completed successfully!\")\n",
        "        else:\n",
        "            print(\"\\n⚠️  Training encountered issues, trying fallback...\")\n",
        "            train_with_api_fallback()\n",
        "            \n",
        "    except KeyboardInterrupt:\n",
        "        print(\"\\n⏹️ Training interrupted by user\")\n",
        "        print(\"💾 Checkpoints should be saved in /content/tts_training/checkpoints/\")\n",
        "    except Exception as e:\n",
        "        print(f\"\\n❌ Training error: {e}\")\n",
        "        print(\"🔄 Trying fallback method...\")\n",
        "        train_with_api_fallback()"
      ],
      "metadata": {
        "id": \"start_training_cell\"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ================================\n",
        "# 10. INFERENCE AND TESTING\n",
        "# ================================\n",
        "\n",
        "def test_trained_model():\n",
        "    \"\"\"Test the trained model\"\"\"\n",
        "    print(\"🎤 Testing trained model...\")\n",
        "    \n",
        "    # Find latest checkpoint\n",
        "    checkpoint_dir = '/content/tts_training/checkpoints'\n",
        "    if not os.path.exists(checkpoint_dir):\n",
        "        print(\"❌ No checkpoints directory found\")\n",
        "        return\n",
        "    \n",
        "    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]\n",
        "    if not checkpoint_files:\n",
        "        print(\"❌ No checkpoint files found\")\n",
        "        return\n",
        "    \n",
        "    # Get the latest checkpoint\n",
        "    latest_checkpoint = max(checkpoint_files, \n",
        "                          key=lambda x: os.path.getmtime(os.path.join(checkpoint_dir, x)))\n",
        "    checkpoint_path = os.path.join(checkpoint_dir, latest_checkpoint)\n",
        "    \n",
        "    print(f\"🔍 Found checkpoint: {latest_checkpoint}\")\n",
        "    print(f\"📁 Size: {os.path.getsize(checkpoint_path) / (1024*1024):.2f} MB\")\n",
        "    \n",
        "    # Test texts\n",
        "    test_texts = [\n",
        "        \"Xin chào, tôi là trợ lý ảo tiếng Việt.\",\n",
        "        \"Hôm nay trời đẹp quá!\",\n",
        "        \"Cảm ơn bạn đã sử dụng dịch vụ.\"\n",
        "    ]\n",
        "    \n",
        "    try:\n",
        "        from TTS.api import TTS\n",
        "        \n",
        "        # Try to load the trained model\n",
        "        config_path = '/content/tts_training/config.json'\n",
        "        \n",
        "        print(\"🔄 Loading trained model...\")\n",
        "        tts = TTS(model_path=checkpoint_path, config_path=config_path)\n",
        "        \n",
        "        # Generate test audio\n",
        "        for i, text in enumerate(test_texts):\n",
        "            output_path = f'/content/tts_training/test_output_{i+1}.wav'\n",
        "            print(f\"🎵 Generating: {text}\")\n",
        "            \n",
        "            tts.tts_to_file(text=text, file_path=output_path)\n",
        "            print(f\"✅ Saved: {output_path}\")\n",
        "        \n",
        "        print(\"\\n🎉 Test audio generation completed!\")\n",
        "        print(\"📥 You can download the generated audio files.\")\n",
        "        \n",
        "    except Exception as e:\n",
        "        print(f\"❌ Model testing failed: {e}\")\n",
        "        print(\"\\n🔧 Manual inference setup:\")\n",
        "        \n",
        "        inference_code = f'''\n",
        "# Manual inference code\n",
        "from TTS.api import TTS\n",
        "\n",
        "# Load your trained model\n",
        "model_path = \"{checkpoint_path}\"\n",
        "config_path = \"/content/tts_training/config.json\"\n",
        "\n",
        "# Initialize TTS\n",
        "tts = TTS(model_path=model_path, config_path=config_path)\n",
        "\n",
        "# Generate speech\n",
        "text = \"Xin chào, đây là giọng nói tiếng Việt.\"\n",
        "output_path = \"/content/vietnamese_output.wav\"\n",
        "tts.tts_to_file(text=text, file_path=output_path)\n",
        "\n",
        "print(f\"Audio saved to: {{output_path}}\")\n",
        "'''\n",
        "        \n",
        "        print(inference_code)\n",
        "        \n",
        "        # Save inference script\n",
        "        with open('/content/inference_script.py', 'w', encoding='utf-8') as f:\n",
        "            f.write(inference_code)\n",
        "        \n",
        "        print(\"💾 Inference script saved to: /content/inference_script.py\")\n",
        "\n",
        "# Run the test\n",
        "test_trained_model()"
      ],
      "metadata": {
        "id": \"inference_cell\"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "markdown",
      "source": [
        "# 🎉 Vietnamese TTS Training Complete!\n",
        "\n",
        "## What was fixed in this version:\n",
        "\n",
        "1. **✅ Custom Vietnamese Formatter**: Created a proper formatter that handles Vietnamese text and metadata format\n",
        "2. **✅ Import Error Fixes**: Removed problematic imports and used stable TTS API approaches\n",
        "3. **✅ Data Validation**: Added comprehensive validation to check data before training\n",
        "4. **✅ Improved Error Handling**: Multiple fallback methods for training\n",
        "5. **✅ Better Text Cleaning**: Enhanced Vietnamese text processing\n",
        "\n",
        "## Key improvements:\n",
        "\n",
        "- **No more \"formatter not found\" errors**\n",
        "- **Proper metadata handling** for Vietnamese datasets\n",
        "- **Robust training pipeline** with fallbacks\n",
        "- **Real-time training monitoring**\n",
        "- **Automatic model testing** after training\n",
        "\n",
        "## Next steps:\n",
        "\n",
        "1. **Monitor training**: Watch the training progress and loss values\n",
        "2. **Test the model**: Use the inference cell to generate Vietnamese speech\n",
        "3. **Fine-tune**: Adjust hyperparameters if needed\n",
        "4. **Deploy**: Use the trained model for your Vietnamese TTS applications\n",
        "\n",
        "## Troubleshooting:\n",
        "\n",
        "If you still encounter issues:\n",
        "- Check your metadata format: `filename|transcript`\n",
        "- Ensure WAV files are in the correct directory\n",
        "- Verify audio file quality and format\n",
        "- Monitor GPU memory usage\n",
        "\n",
        "**Happy training! 🚀**\n"
      ],
      "metadata": {
        "id": \"completion_cell\"
      }
    }
  ]
}
