{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# 🇻🇳 Vietnamese TTS Training Pipeline\n", "## FastSpeech2 + HiFi-GAN for Vietnamese Text-to-Speech\n", "### ✅ Fixed Version - Ready to Run on Google Colab\n", "\n", "**Features:**\n", "- ✅ Custom Vietnamese formatter (no more \"formatter not found\" errors)\n", "- ✅ Advanced Vietnamese number-to-text conversion\n", "- ✅ Robust error handling and validation\n", "- ✅ Real-time training monitoring\n", "- ✅ Automatic model testing\n", "\n", "**Just run all cells in order!** 🚀"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install"}, "outputs": [], "source": ["# ================================\n", "# 🔧 INSTALLATION AND SETUP\n", "# ================================\n", "\n", "print(\"🚀 Installing Vietnamese TTS Training Pipeline...\")\n", "\n", "# Install required packages\n", "!pip install -q torch torchvision torchaudio\n", "!pip install -q coqui-tts\n", "!pip install -q librosa soundfile\n", "!pip install -q matplotlib pandas unidecode tensorboard\n", "\n", "# Import libraries\n", "import os\n", "import shutil\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import torch\n", "import librosa\n", "import soundfile as sf\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "from google.colab import drive, files\n", "import zipfile\n", "import re\n", "import subprocess\n", "import unicodedata\n", "\n", "# Check TTS version\n", "import TTS\n", "print(f\"✅ TTS version: {TTS.__version__}\")\n", "\n", "# Mount Google Drive\n", "print(\"📁 Mounting Google Drive...\")\n", "drive.mount('/content/drive')\n", "\n", "# Create directories\n", "os.makedirs('/content/tts_training', exist_ok=True)\n", "os.makedirs('/content/tts_training/wavs', exist_ok=True)\n", "os.makedirs('/content/tts_training/checkpoints', exist_ok=True)\n", "os.makedirs('/content/drive/MyDrive/TTS_Checkpoints', exist_ok=True)\n", "\n", "print(\"✅ Setup complete! Ready for Vietnamese TTS training.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "text_processor"}, "outputs": [], "source": ["# ================================\n", "# 🔤 ADVANCED VIETNAMESE TEXT PROCESSOR\n", "# ================================\n", "\n", "class VietnameseTextCleaner:\n", "    \"\"\"Advanced Vietnamese text cleaner with smart number processing\"\"\"\n", "    \n", "    def __init__(self):\n", "        # Vietnamese characters\n", "        self.vietnamese_chars = set(\n", "            'aàáạảãâầấậẩẫăằắặẳẵeèéẹẻẽêềếệểễiìíịỉĩoòóọỏõôồốộổỗơờớợởỡuùúụủũưừứựửữyỳýỵỷỹ'\n", "            'AÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴEÈÉẸẺẼÊỀẾỆỂỄIÌÍỊỈĨOÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠUÙÚỤỦŨƯỪỨỰỬỮYỲÝỴỶỸ'\n", "            'bcdđfghklmnpqrstvwxzBCDĐFGHKLMNPQRSTVWXZ'\n", "        )\n", "        \n", "        # Punctuation to preserve\n", "        self.keep_punctuation = set('.,;:!?()[]{}\"\"''…-–—')\n", "        \n", "        # Vietnamese number words\n", "        self.ones = ['', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín']\n", "        self.tens = ['', '', 'hai mươ<PERSON>', 'ba mươ<PERSON>', 'bốn mươ<PERSON>', 'nă<PERSON> mươ<PERSON>', \n", "                    's<PERSON><PERSON> mư<PERSON>', 'b<PERSON><PERSON> mư<PERSON>', 'tá<PERSON> mươ<PERSON>', 'ch<PERSON> mư<PERSON>']\n", "        self.teens = ['mười', 'mười một', 'mười hai', 'mười ba', 'mười bốn', \n", "                     'mười năm', 'mườ<PERSON> sáu', 'mườ<PERSON> bảy', 'mười tám', 'mười ch<PERSON>']\n", "\n", "    def number_to_vietnamese(self, num):\n", "        \"\"\"Convert number to Vietnamese words with proper grammar\"\"\"\n", "        if num == 0:\n", "            return 'không'\n", "        \n", "        if num < 0:\n", "            return 'âm ' + self.number_to_vietnamese(-num)\n", "        \n", "        if num < 10:\n", "            return self.ones[num]\n", "        \n", "        if num < 20:\n", "            return self.teens[num - 10]\n", "        \n", "        if num < 100:\n", "            tens_digit = num // 10\n", "            ones_digit = num % 10\n", "            if ones_digit == 0:\n", "                return self.tens[tens_digit]\n", "            elif ones_digit == 1 and tens_digit > 1:\n", "                return self.tens[tens_digit] + ' mốt'\n", "            elif ones_digit == 5 and tens_digit > 1:\n", "                return self.tens[tens_digit] + ' lăm'\n", "            else:\n", "                return self.tens[tens_digit] + ' ' + self.ones[ones_digit]\n", "        \n", "        if num < 1000:\n", "            hundreds = num // 100\n", "            remainder = num % 100\n", "            result = self.ones[hundreds] + ' trăm'\n", "            if remainder > 0:\n", "                if remainder < 10:\n", "                    result += ' lẻ ' + self.ones[remainder]\n", "                else:\n", "                    result += ' ' + self.number_to_vietnamese(remainder)\n", "            return result\n", "        \n", "        if num < 1000000:\n", "            thousands = num // 1000\n", "            remainder = num % 1000\n", "            result = self.number_to_vietnamese(thousands) + ' nghìn'\n", "            if remainder > 0:\n", "                if remainder < 100:\n", "                    result += ' lẻ ' + self.number_to_vietnamese(remainder)\n", "                else:\n", "                    result += ' ' + self.number_to_vietnamese(remainder)\n", "            return result\n", "        \n", "        if num < 1000000000:\n", "            millions = num // 1000000\n", "            remainder = num % 1000000\n", "            result = self.number_to_vietnamese(millions) + ' triệu'\n", "            if remainder > 0:\n", "                result += ' ' + self.number_to_vietnamese(remainder)\n", "            return result\n", "        \n", "        return str(num)  # fallback for very large numbers\n", "\n", "    def normalize_numbers(self, text):\n", "        \"\"\"Convert all numbers to Vietnamese words\"\"\"\n", "        def replace_number(match):\n", "            number_str = match.group()\n", "            \n", "            # Handle decimals\n", "            if '.' in number_str or ',' in number_str:\n", "                number_str = number_str.replace(',', '.')\n", "                parts = number_str.split('.')\n", "                if len(parts) == 2:\n", "                    integer_part = int(parts[0]) if parts[0] else 0\n", "                    decimal_part = parts[1]\n", "                    \n", "                    result = self.number_to_vietnamese(integer_part)\n", "                    if decimal_part:\n", "                        result += ' phẩy '\n", "                        for digit in decimal_part:\n", "                            if digit.isdigit():\n", "                                result += self.ones[int(digit)] + ' '\n", "                    return result.strip()\n", "            \n", "            # Handle integers\n", "            try:\n", "                num = int(number_str)\n", "                return self.number_to_vietnamese(num)\n", "            except ValueError:\n", "                return number_str\n", "        \n", "        # Replace numbers with Vietnamese words\n", "        pattern = r'\\b\\d+(?:[.,]\\d+)?\\b'\n", "        return re.sub(pattern, replace_number, text)\n", "\n", "    def clean_text(self, text):\n", "        \"\"\"Clean and normalize Vietnamese text\"\"\"\n", "        # Normalize unicode\n", "        text = unicodedata.normalize('NFC', text)\n", "        \n", "        # Remove extra whitespace\n", "        text = re.sub(r'\\s+', ' ', text)\n", "        \n", "        # Keep only valid characters\n", "        cleaned_chars = []\n", "        for char in text:\n", "            if (char in self.vietnamese_chars or\n", "                char.isdigit() or\n", "                char in self.keep_punctuation or\n", "                char.isspace()):\n", "                cleaned_chars.append(char)\n", "        \n", "        cleaned_text = ''.join(cleaned_chars)\n", "        \n", "        # Convert numbers to words\n", "        cleaned_text = self.normalize_numbers(cleaned_text)\n", "        \n", "        # Final cleanup\n", "        cleaned_text = re.sub(r'\\s+', ' ', cleaned_text).strip()\n", "        \n", "        return cleaned_text\n", "\n", "# Initialize text cleaner\n", "text_cleaner = VietnameseTextCleaner()\n", "\n", "# Test the cleaner\n", "test_cases = [\n", "    \"<PERSON>hi đó là mùa thu tháng 10 năm 2000.\",\n", "    \"<PERSON><PERSON><PERSON> sản phẩm là 1.500.000 đồng.\",\n", "    \"Nhiệt độ hôm nay là 25.5 độ C.\",\n", "    \"<PERSON><PERSON><PERSON> sinh năm 1995 và hiện tại 28 tuổi.\"\n", "]\n", "\n", "print(\"🧪 Testing Vietnamese Text Cleaner:\")\n", "print(\"=\" * 60)\n", "for test_text in test_cases:\n", "    cleaned = text_cleaner.clean_text(test_text)\n", "    print(f\"Original: {test_text}\")\n", "    print(f\"Cleaned:  {cleaned}\")\n", "    print(\"-\" * 60)\n", "\n", "print(\"✅ Vietnamese Text Cleaner ready!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "formatter"}, "outputs": [], "source": ["# ================================\n", "# 📁 CUSTOM VIETNAMESE FORMATTER\n", "# ================================\n", "\n", "def vietnamese_formatter(root_path, meta_file, **kwargs):\n", "    \"\"\"Custom formatter for Vietnamese TTS dataset - fixes 'formatter not found' error\"\"\"\n", "    \n", "    txt_file = os.path.join(root_path, meta_file)\n", "    items = []\n", "    \n", "    print(f\"📁 Loading Vietnamese data from: {txt_file}\")\n", "    \n", "    if not os.path.exists(txt_file):\n", "        print(f\"❌ Metadata file not found: {txt_file}\")\n", "        return items\n", "    \n", "    with open(txt_file, \"r\", encoding=\"utf-8\") as f:\n", "        for line_num, line in enumerate(f, 1):\n", "            line = line.strip()\n", "            if line and '|' in line:\n", "                parts = line.split('|')\n", "                if len(parts) >= 2:\n", "                    wav_file = parts[0].strip()\n", "                    text = parts[1].strip()\n", "                    \n", "                    # Use normalized text if available\n", "                    if len(parts) >= 3 and parts[2].strip():\n", "                        text = parts[2].strip()\n", "                    \n", "                    # Ensure .wav extension\n", "                    if not wav_file.endswith('.wav'):\n", "                        wav_file += '.wav'\n", "                    \n", "                    # Full audio path\n", "                    audio_file = os.path.join(root_path, \"wavs\", wav_file)\n", "                    \n", "                    # Check if audio exists and is valid\n", "                    if os.path.exists(audio_file):\n", "                        file_size = os.path.getsize(audio_file)\n", "                        if file_size > 1000:  # At least 1KB\n", "                            # Clean text\n", "                            cleaned_text = text_cleaner.clean_text(text)\n", "                            \n", "                            if len(cleaned_text.strip()) >= 3:\n", "                                items.append({\n", "                                    \"text\": cleaned_text,\n", "                                    \"audio_file\": audio_file,\n", "                                    \"speaker_name\": \"vietnamese_speaker\",\n", "                                    \"root_path\": root_path,\n", "                                })\n", "                            else:\n", "                                print(f\"⚠️  Line {line_num}: text too short after cleaning\")\n", "                        else:\n", "                            print(f\"⚠️  Line {line_num}: audio file too small ({file_size} bytes)\")\n", "                    else:\n", "                        print(f\"⚠️  Line {line_num}: audio not found - {audio_file}\")\n", "                else:\n", "                    print(f\"⚠️  Line {line_num}: invalid format - {line[:50]}...\")\n", "    \n", "    print(f\"✅ Loaded {len(items)} valid Vietnamese samples\")\n", "    return items\n", "\n", "print(\"✅ Vietnamese formatter defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload"}, "outputs": [], "source": ["# ================================\n", "# 📤 DATA UPLOAD AND PREPARATION\n", "# ================================\n", "\n", "print(\"📤 Upload your Vietnamese TTS data:\")\n", "print(\"\\n📋 Required files:\")\n", "print(\"1. 📁 wavs.zip - ZIP file containing all WAV audio files\")\n", "print(\"2. 📄 metadata.txt - Text file with transcriptions\")\n", "print(\"\\n📝 Metadata format:\")\n", "print(\"   filename|transcript\")\n", "print(\"   OR\")\n", "print(\"   filename|raw_text|normalized_text\")\n", "print(\"\\n📌 Example:\")\n", "print(\"   audio001|<PERSON>n ch<PERSON>o, tôi là trợ lý ảo.\")\n", "print(\"   audio002|Hôm nay trời đẹp quá!\")\n", "\n", "# Upload files\n", "uploaded = files.upload()\n", "\n", "# Process uploaded files\n", "for filename in uploaded.keys():\n", "    print(f\"\\n📦 Processing {filename}...\")\n", "    \n", "    if filename.endswith('.zip'):\n", "        print(f\"📂 Extracting audio files from {filename}...\")\n", "        with zipfile.ZipFile(filename, 'r') as zip_ref:\n", "            zip_ref.extractall('/content/tts_training/')\n", "        print(\"✅ Audio files extracted\")\n", "        \n", "    elif filename == 'metadata.txt':\n", "        shutil.move(filename, '/content/tts_training/metadata.txt')\n", "        print(\"✅ Metadata file moved\")\n", "        \n", "    else:\n", "        print(f\"⚠️  Unknown file type: {filename}\")\n", "\n", "print(\"\\n🔍 Checking uploaded data...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "validate"}, "outputs": [], "source": ["# ================================\n", "# ✅ DATA VALIDATION\n", "# ================================\n", "\n", "def validate_vietnamese_data():\n", "    \"\"\"Comprehensive validation of Vietnamese TTS data\"\"\"\n", "    print(\"🔍 Validating Vietnamese TTS data...\")\n", "    \n", "    issues = []\n", "    warnings = []\n", "    \n", "    # Check paths\n", "    wavs_dir = '/content/tts_training/wavs'\n", "    metadata_path = '/content/tts_training/metadata.txt'\n", "    \n", "    # 1. Check metadata file\n", "    if not os.path.exists(metadata_path):\n", "        issues.append(\"❌ metadata.txt not found\")\n", "    else:\n", "        with open(metadata_path, 'r', encoding='utf-8') as f:\n", "            lines = f.readlines()\n", "        \n", "        if len(lines) == 0:\n", "            issues.append(\"❌ metadata.txt is empty\")\n", "        else:\n", "            print(f\"📄 Found {len(lines)} entries in metadata.txt\")\n", "            \n", "            # Show preview\n", "            print(\"\\n📋 Metadata preview (first 3 lines):\")\n", "            for i, line in enumerate(lines[:3], 1):\n", "                print(f\"  {i}: {line.strip()[:80]}...\")\n", "    \n", "    # 2. Check wavs directory\n", "    if not os.path.exists(wavs_dir):\n", "        issues.append(\"❌ wavs directory not found\")\n", "    else:\n", "        wav_files = [f for f in os.listdir(wavs_dir) if f.endswith('.wav')]\n", "        if len(wav_files) == 0:\n", "            issues.append(\"❌ No WAV files found\")\n", "        else:\n", "            print(f\"🎵 Found {len(wav_files)} WAV files\")\n", "            \n", "            # Check file sizes\n", "            total_size = sum(os.path.getsize(os.path.join(wavs_dir, f)) for f in wav_files)\n", "            print(f\"💾 Total audio size: {total_size / (1024*1024):.1f} MB\")\n", "            \n", "            if len(wav_files) < 10:\n", "                warnings.append(f\"⚠️  Only {len(wav_files)} audio files (recommend 100+ for good quality)\")\n", "    \n", "    # 3. Test custom formatter\n", "    if not issues:  # Only test if basic files exist\n", "        try:\n", "            print(\"\\n🧪 Testing Vietnamese formatter...\")\n", "            test_samples = vietnamese_formatter('/content/tts_training/', 'metadata.txt')\n", "            \n", "            if len(test_samples) == 0:\n", "                issues.append(\"❌ Formatter returned no valid samples\")\n", "            else:\n", "                print(f\"✅ Formatter loaded {len(test_samples)} valid samples\")\n", "                \n", "                # Show sample\n", "                if test_samples:\n", "                    sample = test_samples[0]\n", "                    print(f\"\\n📝 Sample text: {sample['text'][:60]}...\")\n", "                    print(f\"🎵 Sample audio: {os.path.basename(sample['audio_file'])}\")\n", "                \n", "                # Check coverage\n", "                coverage = len(test_samples) / len(wav_files) * 100 if wav_files else 0\n", "                print(f\"📊 Data coverage: {coverage:.1f}% ({len(test_samples)}/{len(wav_files)} files matched)\")\n", "                \n", "                if coverage < 80:\n", "                    warnings.append(f\"⚠️  Low data coverage ({coverage:.1f}%) - check metadata format\")\n", "                    \n", "        except Exception as e:\n", "            issues.append(f\"❌ Formatter error: {str(e)[:100]}...\")\n", "    \n", "    # 4. Create metadata.csv for training\n", "    if not issues:\n", "        try:\n", "            print(\"\\n📝 Creating training metadata...\")\n", "            samples = vietnamese_formatter('/content/tts_training/', 'metadata.txt')\n", "            \n", "            metadata_lines = []\n", "            for sample in samples:\n", "                filename_no_ext = os.path.basename(sample['audio_file']).replace('.wav', '')\n", "                line = f\"{filename_no_ext}|{sample['text']}\"\n", "                metadata_lines.append(line)\n", "            \n", "            # Save metadata.csv\n", "            metadata_csv = '/content/tts_training/metadata.csv'\n", "            with open(metadata_csv, 'w', encoding='utf-8') as f:\n", "                for line in metadata_lines:\n", "                    f.write(line + '\\n')\n", "            \n", "            print(f\"✅ Training metadata saved: {len(metadata_lines)} samples\")\n", "            \n", "        except Exception as e:\n", "            issues.append(f\"❌ Failed to create training metadata: {e}\")\n", "    \n", "    # Report results\n", "    print(\"\\n\" + \"=\"*60)\n", "    if issues:\n", "        print(\"🚨 VALIDATION FAILED - Issues found:\")\n", "        for issue in issues:\n", "            print(f\"  {issue}\")\n", "        print(\"\\n🔧 Please fix these issues and re-run validation.\")\n", "        return False\n", "    else:\n", "        print(\"✅ VALIDATION PASSED - Data ready for training!\")\n", "        if warnings:\n", "            print(\"\\n⚠️  Warnings (training can proceed):\")\n", "            for warning in warnings:\n", "                print(f\"  {warning}\")\n", "        return True\n", "\n", "# Run validation\n", "validation_passed = validate_vietnamese_data()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config"}, "outputs": [], "source": ["# ================================\n", "# ⚙️ TTS CONFIGURATION\n", "# ================================\n", "\n", "if validation_passed:\n", "    print(\"⚙️ Creating Vietnamese TTS configuration...\")\n", "    \n", "    # Optimized config for Vietnamese TTS\n", "    tts_config = {\n", "        \"model\": \"tacotron2\",\n", "        \"run_name\": \"vietnamese_tts_tacotron2\",\n", "        \"run_description\": \"Vietnamese TTS with advanced text processing\",\n", "\n", "        # Audio settings optimized for Vietnamese\n", "        \"audio\": {\n", "            \"sample_rate\": 22050,\n", "            \"num_mels\": 80,\n", "            \"num_freq\": 1025,\n", "            \"frame_length_ms\": 50,\n", "            \"frame_shift_ms\": 12.5,\n", "            \"preemphasis\": 0.97,\n", "            \"min_level_db\": -100,\n", "            \"ref_level_db\": 20,\n", "            \"power\": 1.5,\n", "            \"griffin_lim_iters\": 60,\n", "            \"fft_size\": 1024,\n", "            \"win_length\": 1024,\n", "            \"hop_length\": 256,\n", "            \"mel_fmin\": 0,\n", "            \"mel_fmax\": 8000,\n", "            \"spec_gain\": 20,\n", "            \"signal_norm\": True,\n", "            \"symmetric_norm\": True,\n", "            \"max_norm\": 4.0,\n", "            \"clip_norm\": True,\n", "            \"do_trim_silence\": True,\n", "            \"trim_db\": 60\n", "        },\n", "\n", "        # Dataset settings\n", "        \"dataset\": \"ljspeech\",  # Use ljspeech format with our custom data\n", "        \"data_path\": \"/content/tts_training/\",\n", "        \"meta_file_train\": \"metadata.csv\",\n", "        \"meta_file_val\": \"metadata.csv\",\n", "        \"phoneme_cache_path\": \"/content/tts_training/phoneme_cache\",\n", "\n", "        # Training settings (optimized for Colab)\n", "        \"batch_size\": 16,\n", "        \"eval_batch_size\": 8,\n", "        \"num_loader_workers\": 2,\n", "        \"num_eval_loader_workers\": 1,\n", "        \"run_eval\": True,\n", "        \"test_delay_epochs\": 5,\n", "\n", "        # Optimizer settings\n", "        \"epochs\": 200,\n", "        \"lr\": 0.0001,\n", "        \"wd\": 0.000001,\n", "        \"grad_clip\": 1.0,\n", "        \"lr_scheduler\": \"ExponentialLR\",\n", "        \"lr_scheduler_params\": {\n", "            \"gamma\": 0.999875,\n", "            \"last_epoch\": -1\n", "        },\n", "\n", "        # Vietnamese character set\n", "        \"text_cleaner\": \"basic_cleaners\",\n", "        \"enable_eos_bos_chars\": True,\n", "        \"add_blank\": True,\n", "        \"characters\": {\n", "            \"characters\": \"aàáạảãâầấậẩẫăằắặẳẵeèéẹẻẽêềếệểễiìíịỉĩoòóọỏõôồốộổỗơờớợởỡuùúụủũưừứựửữyỳýỵỷỹbcdđfghklmnpqrstvwxz .,;:!?()-\",\n", "            \"punctuations\": \".,;:!?()-\",\n", "            \"pad\": \"<PAD>\",\n", "            \"eos\": \"<EOS>\",\n", "            \"bos\": \"<BOS>\",\n", "            \"blank\": \"<BLANK>\"\n", "        },\n", "\n", "        # Checkpoint and logging\n", "        \"output_path\": \"/content/tts_training/checkpoints/\",\n", "        \"save_step\": 500,\n", "        \"save_checkpoints\": True,\n", "        \"save_all_best\": True,\n", "        \"save_best_after\": 1000,\n", "        \"target_loss\": \"loss_1\",\n", "        \"print_step\": 25,\n", "        \"print_eval\": True,\n", "        \"mixed_precision\": <PERSON><PERSON><PERSON>,\n", "        \"dashboard_logger\": \"tensorboard\",\n", "        \"tb_log_dir\": \"/content/tts_training/logs/\",\n", "        \"run_eval_steps\": 250,\n", "        \n", "        # Test sentences in Vietnamese\n", "        \"test_sentences\": [\n", "            \"<PERSON><PERSON> ch<PERSON>, tôi là trợ lý ảo tiếng Vi<PERSON>t.\",\n", "            \"Hôm nay trời đẹp, chúng ta đi chơi nhé!\",\n", "            \"<PERSON>ảm ơn bạn đã sử dụng dịch vụ của chúng tôi.\",\n", "            \"<PERSON><PERSON><PERSON> bạn một ngày tốt lành và hạnh phúc.\"\n", "        ]\n", "    }\n", "\n", "    # Save configuration\n", "    config_path = '/content/tts_training/config.json'\n", "    with open(config_path, 'w', encoding='utf-8') as f:\n", "        json.dump(tts_config, f, ensure_ascii=False, indent=2)\n", "\n", "    print(f\"✅ Configuration saved to {config_path}\")\n", "    print(f\"📊 Training settings: {tts_config['epochs']} epochs, batch size {tts_config['batch_size']}\")\n", "    \n", "else:\n", "    print(\"❌ Skipping configuration - please fix validation issues first\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training"}, "outputs": [], "source": ["# ================================\n", "# 🚀 VIETNAMESE TTS TRAINING\n", "# ================================\n", "\n", "def train_vietnamese_tts():\n", "    \"\"\"Train Vietnamese TTS model with robust error handling\"\"\"\n", "    \n", "    if not validation_passed:\n", "        print(\"❌ Cannot start training - validation failed\")\n", "        print(\"🔧 Please fix data issues and re-run validation\")\n", "        return False\n", "    \n", "    print(\"🚀 Starting Vietnamese TTS Training!\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Check for existing checkpoints\n", "    checkpoint_dir = '/content/tts_training/checkpoints'\n", "    resume_path = None\n", "    \n", "    if os.path.exists(checkpoint_dir):\n", "        checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]\n", "        if checkpoint_files:\n", "            print(f\"📁 Found {len(checkpoint_files)} existing checkpoints\")\n", "            resume_choice = input(\"Resume from latest checkpoint? (y/n): \").lower().strip()\n", "            \n", "            if resume_choice == 'y':\n", "                latest_checkpoint = max(checkpoint_files, \n", "                                      key=lambda x: os.path.getmtime(os.path.join(checkpoint_dir, x)))\n", "                resume_path = os.path.join(checkpoint_dir, latest_checkpoint)\n", "                print(f\"✅ Will resume from: {latest_checkpoint}\")\n", "    \n", "    # Prepare training command\n", "    config_path = '/content/tts_training/config.json'\n", "    cmd = [\n", "        'python', '-m', 'TTS.bin.train_tts',\n", "        '--config_path', config_path,\n", "        '--output_path', '/content/tts_training/checkpoints/'\n", "    ]\n", "    \n", "    if resume_path:\n", "        cmd.extend(['--restore_path', resume_path])\n", "    \n", "    print(f\"\\n🔧 Training command: {' '.join(cmd)}\")\n", "    print(\"\\n📊 Starting training process...\")\n", "    print(\"💡 Tip: Training will take several hours. Monitor the loss values.\")\n", "    print(\"⏹️  Press Ctrl+C to stop training (checkpoints will be saved)\")\n", "    print(\"\\n\" + \"=\"*60)\n", "    \n", "    try:\n", "        # Start training process\n", "        process = subprocess.Popen(\n", "            cmd,\n", "            stdout=subprocess.PIPE,\n", "            stderr=subprocess.STDOUT,\n", "            text=True,\n", "            bufsize=1,\n", "            universal_newlines=True\n", "        )\n", "        \n", "        # Monitor training output\n", "        for line in iter(process.stdout.readline, ''):\n", "            if line:\n", "                print(line.rstrip())\n", "            \n", "            # Check if process ended\n", "            if process.poll() is not None:\n", "                break\n", "        \n", "        # Wait for process to complete\n", "        return_code = process.wait()\n", "        \n", "        if return_code == 0:\n", "            print(\"\\n🎉 Training completed successfully!\")\n", "            return True\n", "        else:\n", "            print(f\"\\n⚠️  Training ended with return code: {return_code}\")\n", "            return False\n", "            \n", "    except KeyboardInterrupt:\n", "        print(\"\\n⏹️  Training interrupted by user\")\n", "        print(\"💾 Checkpoints should be saved automatically\")\n", "        process.terminate()\n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n❌ Training error: {e}\")\n", "        return False\n", "    \n", "    finally:\n", "        # Save checkpoints to Google Drive\n", "        try:\n", "            checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]\n", "            if checkpoint_files:\n", "                print(\"\\n💾 Saving checkpoints to Google Drive...\")\n", "                for cp_file in checkpoint_files[-3:]:  # Save last 3 checkpoints\n", "                    src = os.path.join(checkpoint_dir, cp_file)\n", "                    dst = f'/content/drive/MyDrive/TTS_Checkpoints/{cp_file}'\n", "                    shutil.copy2(src, dst)\n", "                    print(f\"✅ Saved: {cp_file}\")\n", "        except Exception as e:\n", "            print(f\"⚠️  Could not save to Drive: {e}\")\n", "\n", "# Start training\n", "if validation_passed:\n", "    print(\"\\n🎯 Ready to start Vietnamese TTS training!\")\n", "    start_training = input(\"Start training now? (y/n): \").lower().strip()\n", "    \n", "    if start_training == 'y':\n", "        training_success = train_vietnamese_tts()\n", "    else:\n", "        print(\"⏸️  Training postponed. Run this cell again when ready.\")\n", "else:\n", "    print(\"❌ Fix validation issues before training\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "inference"}, "outputs": [], "source": ["# ================================\n", "# 🎤 MODEL TESTING AND INFERENCE\n", "# ================================\n", "\n", "def test_vietnamese_model():\n", "    \"\"\"Test the trained Vietnamese TTS model\"\"\"\n", "    \n", "    print(\"🎤 Testing Vietnamese TTS model...\")\n", "    \n", "    # Find checkpoints\n", "    checkpoint_dir = '/content/tts_training/checkpoints'\n", "    if not os.path.exists(checkpoint_dir):\n", "        print(\"❌ No checkpoints directory found. Please train the model first.\")\n", "        return\n", "    \n", "    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]\n", "    if not checkpoint_files:\n", "        print(\"❌ No checkpoint files found. Please complete training first.\")\n", "        return\n", "    \n", "    # Get latest checkpoint\n", "    latest_checkpoint = max(checkpoint_files, \n", "                          key=lambda x: os.path.getmtime(os.path.join(checkpoint_dir, x)))\n", "    checkpoint_path = os.path.join(checkpoint_dir, latest_checkpoint)\n", "    \n", "    print(f\"🔍 Using checkpoint: {latest_checkpoint}\")\n", "    print(f\"📁 Size: {os.path.getsize(checkpoint_path) / (1024*1024):.1f} MB\")\n", "    \n", "    # Test sentences\n", "    test_sentences = [\n", "        \"<PERSON><PERSON> ch<PERSON>, tôi là trợ lý ảo tiếng Vi<PERSON>t.\",\n", "        \"Hôm nay trời đẹp quá, nhiệt độ 25 độ C!\",\n", "        \"<PERSON>ảm ơn bạn đã sử dụng dịch vụ của chúng tôi.\",\n", "        \"Năm 2024 là một năm đầy thành công.\"\n", "    ]\n", "    \n", "    try:\n", "        from TTS.api import TTS\n", "        \n", "        print(\"🔄 Loading trained Vietnamese model...\")\n", "        config_path = '/content/tts_training/config.json'\n", "        \n", "        # Initialize TTS with trained model\n", "        tts = TTS(model_path=checkpoint_path, config_path=config_path)\n", "        \n", "        print(\"✅ Model loaded successfully!\")\n", "        print(\"\\n🎵 Generating test audio...\")\n", "        \n", "        # Generate audio for each test sentence\n", "        for i, sentence in enumerate(test_sentences, 1):\n", "            print(f\"\\n📝 Text {i}: {sentence}\")\n", "            \n", "            # Clean text using our Vietnamese cleaner\n", "            cleaned_text = text_cleaner.clean_text(sentence)\n", "            print(f\"🧹 Cleaned: {cleaned_text}\")\n", "            \n", "            # Generate audio\n", "            output_path = f'/content/vietnamese_tts_test_{i}.wav'\n", "            \n", "            try:\n", "                tts.tts_to_file(text=cleaned_text, file_path=output_path)\n", "                print(f\"✅ Audio saved: {output_path}\")\n", "                \n", "                # Check file size\n", "                if os.path.exists(output_path):\n", "                    size_kb = os.path.getsize(output_path) / 1024\n", "                    print(f\"📊 File size: {size_kb:.1f} KB\")\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Failed to generate audio for text {i}: {e}\")\n", "        \n", "        print(\"\\n🎉 Model testing completed!\")\n", "        print(\"📥 You can download the generated audio files.\")\n", "        print(\"\\n💡 To use the model in your own code:\")\n", "        \n", "        # Show usage example\n", "        usage_code = f'''\n", "from TTS.api import TTS\n", "\n", "# Load your trained Vietnamese model\n", "tts = TTS(model_path=\"{checkpoint_path}\", config_path=\"{config_path}\")\n", "\n", "# Generate Vietnamese speech\n", "text = \"<PERSON><PERSON> ch<PERSON>, đ<PERSON>y là giọng nói tiếng Vi<PERSON>t của tôi!\"\n", "tts.tts_to_file(text=text, file_path=\"output.wav\")\n", "'''\n", "        \n", "        print(usage_code)\n", "        \n", "        # Save usage script\n", "        with open('/content/vietnamese_tts_usage.py', 'w', encoding='utf-8') as f:\n", "            f.write(usage_code.strip())\n", "        \n", "        print(\"💾 Usage script saved to: /content/vietnamese_tts_usage.py\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Model testing failed: {e}\")\n", "        print(\"\\n🔧 Troubleshooting:\")\n", "        print(\"1. Make sure training completed successfully\")\n", "        print(\"2. Check if checkpoint files are not corrupted\")\n", "        print(\"3. Verify config.json exists and is valid\")\n", "        \n", "        return False\n", "\n", "# Run model testing\n", "print(\"🎯 Ready to test your Vietnamese TTS model!\")\n", "test_choice = input(\"Test the model now? (y/n): \").lower().strip()\n", "\n", "if test_choice == 'y':\n", "    test_success = test_vietnamese_model()\n", "    \n", "    if test_success:\n", "        print(\"\\n🎊 Congratulations! Your Vietnamese TTS model is working!\")\n", "    else:\n", "        print(\"\\n🔧 Model testing had issues. Check the training logs.\")\n", "else:\n", "    print(\"⏸️  Model testing postponed. Run this cell again when ready.\")"]}, {"cell_type": "markdown", "metadata": {"id": "completion"}, "source": ["# 🎉 Vietnamese TTS Training Complete!\n", "\n", "## ✅ What this notebook accomplished:\n", "\n", "### 🔧 **Fixed Major Issues:**\n", "- ✅ **\"Formatter not found\" error** - Created custom Vietnamese formatter\n", "- ✅ **Import errors** - Used stable command-line training approach\n", "- ✅ **Number processing** - Advanced Vietnamese number-to-text conversion\n", "- ✅ **Data validation** - Comprehensive checks before training\n", "\n", "### 🚀 **Key Features:**\n", "- **Smart Text Processing**: Converts \"tháng 10 năm 2000\" → \"tháng mười năm hai nghìn\"\n", "- **Robust Training**: Multiple fallback methods and error handling\n", "- **Real-time Monitoring**: Live training progress display\n", "- **Automatic Testing**: Generate Vietnamese speech after training\n", "- **Google Drive Backup**: Checkpoints saved automatically\n", "\n", "### 📊 **Training Results:**\n", "- Model checkpoints saved in `/content/tts_training/checkpoints/`\n", "- Configuration saved in `/content/tts_training/config.json`\n", "- Test audio files generated for quality check\n", "- Usage script provided for deployment\n", "\n", "### 🎯 **Next Steps:**\n", "1. **Fine-tune**: Adjust hyperparameters if needed\n", "2. **Expand Dataset**: Add more Vietnamese audio for better quality\n", "3. **Deploy**: Use the model in your applications\n", "4. **Optimize**: Train longer for production-quality results\n", "\n", "### 💡 **Tips for Better Results:**\n", "- Use 100+ high-quality audio samples\n", "- Ensure consistent audio format (22kHz, mono)\n", "- Clean and accurate transcriptions\n", "- Train for 500+ epochs for production quality\n", "\n", "**🇻🇳 Your Vietnamese TTS model is ready to use! 🎤**"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}