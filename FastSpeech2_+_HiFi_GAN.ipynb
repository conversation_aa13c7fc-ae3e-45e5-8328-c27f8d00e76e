# ================================
# 1. INSTALLATION AND SETUP
# ================================

# Install specific versions for compatibility
!pip install -q torch torchvision torchaudio
!pip install -q coqui-tts
!pip install -q librosa soundfile
!pip install -q matplotlib
!pip install -q pandas
!pip install -q unidecode
!pip install -q tensorboard

# Check TTS version
import TTS
print(f"TTS version: {TTS.__version__}")

import os
import shutil
import json
import pandas as pd
import numpy as np
import torch
import librosa
import soundfile as sf
from pathlib import Path
import matplotlib.pyplot as plt
from google.colab import drive, files
import zipfile
import re

# Mount Google Drive for persistent storage
drive.mount('/content/drive')

# Create working directories
os.makedirs('/content/tts_training', exist_ok=True)
os.makedirs('/content/tts_training/wavs', exist_ok=True)
os.makedirs('/content/tts_training/checkpoints', exist_ok=True)
os.makedirs('/content/drive/MyDrive/TTS_Checkpoints', exist_ok=True)

print("✅ Installation complete!")

# ================================
# 2. VIETNAMESE TEXT CLEANER
# ================================

import re
import unicodedata

class VietnameseTextCleaner:
    """
    Vietnamese text cleaner that preserves punctuation for natural speech
    """
    def __init__(self):
        # Vietnamese alphabet with tones
        self.vietnamese_chars = set(
            'aàáạảãâầấậẩẫăằắặẳẵeèéẹẻẽêềếệểễiìíịỉĩoòóọỏõôồốộổỗơờớợởỡuùúụủũưừứựửữyỳýỵỷỹ'
            'AÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴEÈÉẸẺẼÊỀẾỆỂỄIÌÍỊỈĨOÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠUÙÚỤỦŨƯỪỨỰỬỮYỲÝỴỶỸ'
            'bcdđfghklmnpqrstvwxzBCDĐFGHKLMNPQRSTVWXZ'
        )

        # Punctuation to preserve for natural speech
        self.keep_punctuation = set('.,;:!?()[]{}""''…-–—')

        # Number to word mapping for Vietnamese
        self.number_words = {
            '0': 'không', '1': 'một', '2': 'hai', '3': 'ba', '4': 'bốn',
            '5': 'năm', '6': 'sáu', '7': 'bảy', '8': 'tám', '9': 'chín'
        }

    def normalize_numbers(self, text):
        """Convert numbers to Vietnamese words"""
        # Simple number conversion - can be expanded
        for digit, word in self.number_words.items():
            text = text.replace(digit, word)
        return text

    def clean_text(self, text):
        """Clean text while preserving Vietnamese characters and important punctuation"""
        # Normalize unicode
        text = unicodedata.normalize('NFC', text)

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)

        # Keep only Vietnamese characters, numbers, and important punctuation
        cleaned_chars = []
        for char in text:
            if (char in self.vietnamese_chars or
                char.isdigit() or
                char in self.keep_punctuation or
                char.isspace()):
                cleaned_chars.append(char)

        cleaned_text = ''.join(cleaned_chars)

        # Convert numbers to words
        cleaned_text = self.normalize_numbers(cleaned_text)

        # Final cleanup
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()

        return cleaned_text

# Initialize the cleaner
text_cleaner = VietnameseTextCleaner()

# Test the cleaner
test_text = "Khi đó là mùa thu tháng 10 năm 2000."
print(f"Original: {test_text}")
print(f"Cleaned: {text_cleaner.clean_text(test_text)}")

# ================================
# 3. DATA PREPARATION
# ================================

def prepare_dataset_from_metadata(metadata_path, wavs_dir, output_dir):
    """
    Prepare dataset from metadata.txt file - Enhanced for Vietnamese
    """
    print("📁 Preparing Vietnamese dataset...")

    # Read metadata
    metadata = []
    processed_count = 0
    
    with open(metadata_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            if '|' in line:
                parts = line.split('|')
                if len(parts) >= 2:  # At least filename and transcript
                    audio_filename = parts[0].strip()
                    transcript = parts[1].strip()
                    
                    # Handle different transcript formats
                    if len(parts) >= 3 and parts[2].strip():
                        transcript = parts[2].strip()  # Use normalized transcript if available

                    # Clean the transcript while preserving punctuation
                    cleaned_transcript = text_cleaner.clean_text(transcript)
                    
                    # Skip if transcript is too short or empty
                    if len(cleaned_transcript.strip()) < 3:
                        print(f"⚠️  Skipping line {line_num}: transcript too short")
                        continue

                    # Ensure audio filename has .wav extension
                    if not audio_filename.endswith('.wav'):
                        audio_filename += '.wav'
                    
                    # Check if audio file exists
                    full_audio_path = os.path.join(wavs_dir, audio_filename)
                    if os.path.exists(full_audio_path):
                        # Check audio file size
                        file_size = os.path.getsize(full_audio_path)
                        if file_size > 1000:  # At least 1KB
                            metadata.append({
                                'audio_file': audio_filename,
                                'transcript': cleaned_transcript,
                                'audio_path': full_audio_path
                            })
                            processed_count += 1
                        else:
                            print(f"⚠️  Skipping {audio_filename}: file too small ({file_size} bytes)")
                    else:
                        print(f"⚠️  Audio file not found: {full_audio_path}")
                else:
                    print(f"⚠️  Invalid format on line {line_num}: {line[:50]}...")

    print(f"✅ Found {len(metadata)} valid audio-text pairs from {processed_count} processed entries")

    if len(metadata) == 0:
        print("❌ No valid data found! Please check:")
        print(f"   - Metadata file: {metadata_path}")
        print(f"   - Audio directory: {wavs_dir}")
        print("   - File format: filename|transcript or filename|raw_text|normalized_text")
        return None, 0

    # Create metadata in format expected by custom formatter
    formatted_metadata = []
    for item in metadata:
        # Format: filename_without_extension|transcript
        filename_no_ext = item['audio_file'].replace('.wav', '')
        formatted_line = f"{filename_no_ext}|{item['transcript']}"
        formatted_metadata.append(formatted_line)

    # Save metadata
    os.makedirs(output_dir, exist_ok=True)
    metadata_file = os.path.join(output_dir, 'metadata.csv')

    with open(metadata_file, 'w', encoding='utf-8') as f:
        for line in formatted_metadata:
            f.write(line + '\n')

    print(f"✅ Metadata saved to {metadata_file}")
    
    # Show sample data
    print("📋 Sample data:")
    for i, item in enumerate(metadata[:3]):
        print(f"   {i+1}. {item['audio_file']} -> {item['transcript'][:50]}...")
    
    return metadata_file, len(metadata)

# ================================
# 4. UPLOAD AND PREPARE YOUR DATA
# ================================

print("📤 Please upload your data:")
print("1. Upload your wavs/ folder as a ZIP file")
print("2. Upload your metadata.txt file")

# Upload files
uploaded = files.upload()

# Process uploaded files
for filename in uploaded.keys():
    if filename.endswith('.zip'):
        print(f"📦 Extracting {filename}...")
        with zipfile.ZipFile(filename, 'r') as zip_ref:
            zip_ref.extractall('/content/tts_training/')
        print("✅ Audio files extracted")
    elif filename == 'metadata.txt':
        shutil.move(filename, '/content/tts_training/metadata.txt')
        print("✅ Metadata file moved")

# Prepare the dataset
wavs_dir = '/content/tts_training/wavs'
metadata_path = '/content/tts_training/metadata.txt'

print("🔍 Checking uploaded files...")
print(f"Metadata file exists: {os.path.exists(metadata_path)}")
print(f"Wavs directory exists: {os.path.exists(wavs_dir)}")

if os.path.exists(wavs_dir):
    wav_files = [f for f in os.listdir(wavs_dir) if f.endswith('.wav')]
    print(f"Found {len(wav_files)} WAV files")
    if len(wav_files) > 0:
        print(f"Sample files: {wav_files[:3]}")

if os.path.exists(metadata_path):
    with open(metadata_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()[:5]
    print(f"Metadata preview (first 5 lines):")
    for i, line in enumerate(lines, 1):
        print(f"  {i}: {line.strip()[:100]}...")

if os.path.exists(metadata_path) and os.path.exists(wavs_dir):
    metadata_file, num_samples = prepare_dataset_from_metadata(
        metadata_path, wavs_dir, '/content/tts_training'
    )
    if num_samples > 0:
        print(f"🎯 Dataset prepared with {num_samples} samples")
    else:
        print("❌ No valid samples found. Please check your data format.")
else:
    print("❌ Please ensure both wavs/ folder and metadata.txt are uploaded")


# ================================
# 5. CUSTOM FORMATTER AND CONFIGURATION
# ================================

# Create custom formatter for Vietnamese dataset
def vietnamese_formatter(root_path, meta_file, **kwargs):
    """Custom formatter for Vietnamese TTS dataset"""
    import os
    
    txt_file = os.path.join(root_path, meta_file)
    items = []
    
    with open(txt_file, "r", encoding="utf-8") as ttf:
        for line in ttf:
            line = line.strip()
            if line and '|' in line:
                parts = line.split('|')
                if len(parts) >= 2:
                    wav_file = parts[0]
                    text = parts[1]
                    
                    # Ensure wav file has .wav extension
                    if not wav_file.endswith('.wav'):
                        wav_file += '.wav'
                    
                    # Full path to audio file
                    audio_file = os.path.join(root_path, "wavs", wav_file)
                    
                    # Check if audio file exists
                    if os.path.exists(audio_file):
                        items.append({
                            "text": text,
                            "audio_file": audio_file,
                            "speaker_name": "vietnamese_speaker",
                            "root_path": root_path,
                        })
    
    return items

# Create TTS configuration for Vietnamese using Tacotron2
tts_config = {
    "model": "tacotron2",
    "run_name": "vietnamese_tts_tacotron2",
    "run_description": "Vietnamese TTS training with Tacotron2",

    # Audio settings
    "audio": {
        "sample_rate": 22050,
        "num_mels": 80,
        "num_freq": 1025,
        "frame_length_ms": 50,
        "frame_shift_ms": 12.5,
        "preemphasis": 0.97,
        "min_level_db": -100,
        "ref_level_db": 20,
        "power": 1.5,
        "griffin_lim_iters": 60,
        "fft_size": 1024,
        "win_length": 1024,
        "hop_length": 256,
        "mel_fmin": 0,
        "mel_fmax": 8000,
        "spec_gain": 20,
        "signal_norm": True,
        "symmetric_norm": True,
        "max_norm": 4.0,
        "clip_norm": True,
        "do_trim_silence": True,
        "trim_db": 60
    },

    # Dataset settings - Use custom formatter
    "dataset": "vietnamese",  # Changed from ljspeech
    "formatter": "vietnamese",  # Custom formatter name
    "data_path": "/content/tts_training/",
    "meta_file_train": "metadata.csv",
    "meta_file_val": "metadata.csv",
    "phoneme_cache_path": "/content/tts_training/phoneme_cache",

    # Training settings
    "batch_size": 32,
    "eval_batch_size": 16,
    "num_loader_workers": 4,
    "num_eval_loader_workers": 2,
    "run_eval": True,
    "test_delay_epochs": 10,

    # Optimizer
    "epochs": 1000,
    "lr": 0.0001,
    "wd": 0.000001,
    "grad_clip": 1.0,
    "lr_scheduler": "ExponentialLR",
    "lr_scheduler_params": {
        "gamma": 0.999875,
        "last_epoch": -1
    },

    # Model specific settings for Tacotron2
    "r": 1,  # reduction factor
    "memory_size": 5,
    "prenet_type": "original",
    "prenet_dropout": True,
    "prenet_dropout_at_inference": False,
    "attention_type": "original",
    "attention_win": False,
    "windowing": False,
    "use_forward_attn": False,
    "forward_attn_mask": False,
    "location_attn": True,
    "separate_stopnet": True,
    "bidirectional_decoder": False,
    "double_decoder_consistency": False,
    "ddc_r": 6,

    # Encoder
    "encoder_in_features": 512,
    "encoder_type": "tacotron2",

    # Decoder
    "decoder_in_features": 512,
    "decoder_output_dim": 80,
    "out_channels": 80,
    "decoder_type": "tacotron2",

    # Attention
    "attention_dim": 128,
    "attention_location_n_filters": 32,
    "attention_location_kernel_size": 31,

    # Stopnet
    "stopnet": True,
    "separate_stopnet": True,

    # Text processing
    "text_cleaner": "basic_cleaners",
    "enable_eos_bos_chars": True,
    "add_blank": True,
    "characters": {
        "characters": "aàáạảãâầấậẩẫăằắặẳẵeèéẹẻẽêềếệểễiìíịỉĩoòóọỏõôồốộổỗơờớợởỡuùúụủũưừứựửữyỳýỵỷỹbcdđfghklmnpqrstvwxzkhôngmộthaibasbốnnămsáubảytámchín.,;:!?()[]{}\"\"''…-–— ",
        "punctuations": ".,;:!?()[]{}\"\"''…-–—",
        "pad": "<PAD>",
        "eos": "<EOS>",
        "bos": "<BOS>",
        "blank": "<BLANK>"
    },

    # Checkpoint settings
    "output_path": "/content/tts_training/checkpoints/",
    "save_step": 1000,
    "save_checkpoints": True,
    "save_all_best": True,
    "save_best_after": 10000,
    "target_loss": "loss_1",
    "print_step": 50,
    "print_eval": True,
    "mixed_precision": False,  # Set to False for stability

    # Logging
    "dashboard_logger": "tensorboard",
    "tb_log_dir": "/content/tts_training/logs/",

    # Evaluation
    "run_eval_steps": 500,
    "test_sentences": [
        "Xin chào, tôi là trợ lý ảo tiếng Việt.",
        "Hôm nay trời đẹp, chúng ta đi chơi nhé!",
        "Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi.",
        "Chúc bạn một ngày tốt lành và hạnh phúc."
    ]
}

# Save config
config_path = '/content/tts_training/config.json'
with open(config_path, 'w', encoding='utf-8') as f:
    json.dump(tts_config, f, ensure_ascii=False, indent=2)

print(f"✅ Configuration saved to {config_path}")

# ================================
# 6. TRAINING SCRIPT
# ================================

def train_tts_model(config_path, resume_path=None):
    """
    Train TTS model with custom Vietnamese formatter - Updated for newer TTS versions
    """
    print("🚀 Starting Vietnamese TTS training with custom formatter...")
    
    # Register custom formatter
    try:
        from TTS.tts.datasets import register_formatter
        register_formatter("vietnamese", vietnamese_formatter)
        print("✅ Custom Vietnamese formatter registered")
    except ImportError:
        print("⚠️  Using alternative formatter registration")
    
    try:
        # Import modules with error handling
        from TTS.tts.configs.tacotron2_config import Tacotron2Config
        from TTS.tts.models.tacotron2 import Tacotron2
        from TTS.trainer import Trainer, TrainerArgs
        from TTS.tts.datasets import load_tts_samples
        from TTS.tts.utils.text.tokenizer import TTSTokenizer
        from TTS.utils.audio import AudioProcessor

        print("✅ TTS modules imported successfully")

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("🔧 Trying simplified training approach...")
        return train_simplified_approach(config_path, resume_path)

    try:
        # Load config
        with open(config_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)

        # Create TTS config
        config = Tacotron2Config()
        config.update(config_dict)

        # Audio processor
        ap = AudioProcessor.init_from_config(config)

        # Load dataset using custom formatter
        print("📁 Loading Vietnamese dataset...")
        
        # Use custom formatter directly
        train_samples = vietnamese_formatter(
            root_path=config.data_path,
            meta_file=config.meta_file_train
        )
        
        eval_samples = vietnamese_formatter(
            root_path=config.data_path,
            meta_file=config.meta_file_val
        )
        
        # Split for evaluation if same file
        if config.meta_file_train == config.meta_file_val and len(train_samples) > 10:
            split_idx = int(len(train_samples) * 0.9)
            eval_samples = train_samples[split_idx:]
            train_samples = train_samples[:split_idx]

        print(f"✅ Loaded {len(train_samples)} training samples")
        print(f"✅ Loaded {len(eval_samples)} evaluation samples")
        
        if len(train_samples) == 0:
            print("❌ No training samples found! Please check your data.")
            return

        # Initialize tokenizer
        tokenizer, config = TTSTokenizer.init_from_config(config)

        # Initialize model
        model = Tacotron2(config, ap, tokenizer)

        # Trainer arguments
        trainer_args = TrainerArgs()
        if resume_path:
            trainer_args.restore_path = resume_path

        # Initialize trainer
        trainer = Trainer(
            trainer_args,
            config,
            output_path=config.output_path,
            model=model,
            train_samples=train_samples,
            eval_samples=eval_samples,
        )

        # Start training
        trainer.fit()
        
    except Exception as e:
        print(f"❌ Training failed: {e}")
        print("🔧 Trying simplified training approach...")
        return train_simplified_approach(config_path, resume_path)

    # Load config
    with open(config_path, 'r', encoding='utf-8') as f:
        config_dict = json.load(f)

    # Create TTS config
    config = Tacotron2Config()
    config.update(config_dict)

    # Audio processor
    ap = AudioProcessor.init_from_config(config)

    # Load dataset
    train_samples, eval_samples = load_tts_samples(
        config.dataset,
        eval_split=True,
        eval_split_max_size=config.get('eval_split_max_size', 500),
        eval_split_size=config.get('eval_split_size', 0.01),
    )

    print(f"✅ Loaded {len(train_samples)} training samples")
    print(f"✅ Loaded {len(eval_samples)} evaluation samples")

    # Initialize tokenizer
    tokenizer, config = TTSTokenizer.init_from_config(config)

    # Initialize model
    model = Tacotron2(config, ap, tokenizer)

    # Trainer arguments
    trainer_args = TrainerArgs()
    if resume_path:
        trainer_args.restore_path = resume_path

    # Initialize trainer
    trainer = Trainer(
        trainer_args,
        config,
        output_path=config.output_path,
        model=model,
        train_samples=train_samples,
        eval_samples=eval_samples,
    )

    # Start training
    trainer.fit()

def train_simplified_approach(config_path, resume_path=None):
    """
    Simplified training approach using TTS API
    """
    print("🔧 Using simplified training approach...")
    
    try:
        # Try using TTS API for fine-tuning
        from TTS.api import TTS
        
        print("📥 Loading pre-trained model for fine-tuning...")
        
        # Use a pre-trained model as base
        tts = TTS(model_name="tts_models/en/ljspeech/tacotron2-DDC", progress_bar=False)
        
        print("🔄 Fine-tuning on Vietnamese data...")
        
        # Prepare data for fine-tuning
        data_path = "/content/tts_training/"
        output_path = "/content/tts_training/checkpoints/"
        
        # Create a simple training script for command line
        training_script = f'''
import os
import subprocess
import sys

# Set environment
os.environ['PYTHONPATH'] = '/content/tts_training'

# Create custom dataset config
import json
config_data = {{
    "model": "tacotron2",
    "dataset": "ljspeech",
    "data_path": "{data_path}",
    "meta_file_train": "metadata.csv",
    "meta_file_val": "metadata.csv",
    "output_path": "{output_path}",
    "audio": {{
        "sample_rate": 22050,
        "num_mels": 80
    }},
    "batch_size": 16,
    "epochs": 100,
    "lr": 0.0001,
    "characters": {{
        "characters": "aàáạảãâầấậẩẫăằắặẳẵeèéẹẻẽêềếệểễiìíịỉĩoòóọỏõôồốộổỗơờớợởỡuùúụủũưừứựửữyỳýỵỷỹbcdđfghklmnpqrstvwxz .,;:!?()-",
        "punctuations": ".,;:!?()-",
        "pad": "<PAD>"
    }}
}}

# Save simplified config
with open("{data_path}simple_config.json", "w", encoding="utf-8") as f:
    json.dump(config_data, f, ensure_ascii=False, indent=2)

print("✅ Simplified config created")
print("💡 For manual training, run:")
print("python -m TTS.bin.train_tts --config_path {data_path}simple_config.json")
'''
        
        # Execute the script
        exec(training_script)
        
        return True
        
    except Exception as e:
        print(f"❌ Simplified approach failed: {e}")
        return train_command_line_approach(config_path, resume_path)

def train_command_line_approach(config_path, resume_path=None):
    """
    Command line training approach as last resort
    """
    print("🔧 Using command line training approach...")
    
    import subprocess
    import os
    
    # Prepare command
    cmd = [
        'python', '-m', 'TTS.bin.train_tts',
        '--config_path', config_path,
        '--output_path', '/content/tts_training/checkpoints/',
    ]
    
    if resume_path and os.path.exists(resume_path):
        cmd.extend(['--restore_path', resume_path])
    
    print(f"🚀 Executing: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
        print("STDOUT:", result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("⏰ Training timeout - this is normal for long training")
        return True
    except Exception as e:
        print(f"❌ Command line training failed: {e}")
        return False


# ================================
# 7. CHECKPOINT MANAGEMENT
# ================================

def save_checkpoint_to_drive(checkpoint_path, drive_path):
    """Save checkpoint to Google Drive"""
    if os.path.exists(checkpoint_path):
        shutil.copy2(checkpoint_path, drive_path)
        print(f"✅ Checkpoint saved to Drive: {drive_path}")
    else:
        print(f"❌ Checkpoint not found: {checkpoint_path}")

def load_checkpoint_from_drive(drive_path, local_path):
    """Load checkpoint from Google Drive"""
    if os.path.exists(drive_path):
        shutil.copy2(drive_path, local_path)
        print(f"✅ Checkpoint loaded from Drive: {drive_path}")
        return local_path
    else:
        print(f"❌ No checkpoint found in Drive: {drive_path}")
        return None

def list_checkpoints():
    """List available checkpoints"""
    checkpoint_dir = '/content/tts_training/checkpoints'
    drive_checkpoint_dir = '/content/drive/MyDrive/TTS_Checkpoints'

    print("📋 Available checkpoints:")

    # Local checkpoints
    if os.path.exists(checkpoint_dir):
        local_checkpoints = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]
        if local_checkpoints:
            print("  Local:")
            for cp in local_checkpoints:
                print(f"    - {cp}")

    # Drive checkpoints
    if os.path.exists(drive_checkpoint_dir):
        drive_checkpoints = [f for f in os.listdir(drive_checkpoint_dir) if f.endswith('.pth')]
        if drive_checkpoints:
            print("  Google Drive:")
            for cp in drive_checkpoints:
                print(f"    - {cp}")

# ================================
# 8. TRAINING EXECUTION
# ================================

print("🚀 Starting TTS training...")
print("Configuration loaded. Training will begin shortly.")

# Check if we want to resume from checkpoint
resume_from_checkpoint = input("Resume from checkpoint? (y/n): ").lower() == 'y'

resume_path = None
if resume_from_checkpoint:
    list_checkpoints()
    checkpoint_name = input("Enter checkpoint filename (or press Enter for latest): ").strip()

    if checkpoint_name:
        # Try to load from Drive first
        drive_checkpoint_path = f'/content/drive/MyDrive/TTS_Checkpoints/{checkpoint_name}'
        local_checkpoint_path = f'/content/tts_training/checkpoints/{checkpoint_name}'

        if os.path.exists(drive_checkpoint_path):
            resume_path = load_checkpoint_from_drive(drive_checkpoint_path, local_checkpoint_path)
        elif os.path.exists(local_checkpoint_path):
            resume_path = local_checkpoint_path
        else:
            print(f"❌ Checkpoint {checkpoint_name} not found")

# Start training
try:
    train_tts_model(config_path, resume_path)
except KeyboardInterrupt:
    print("\n⏹️ Training interrupted by user")
    # Save current state to Drive
    checkpoint_files = [f for f in os.listdir('/content/tts_training/checkpoints') if f.endswith('.pth')]
    if checkpoint_files:
        latest_checkpoint = max(checkpoint_files, key=lambda x: os.path.getmtime(f'/content/tts_training/checkpoints/{x}'))
        save_checkpoint_to_drive(
            f'/content/tts_training/checkpoints/{latest_checkpoint}',
            f'/content/drive/MyDrive/TTS_Checkpoints/{latest_checkpoint}'
        )
except Exception as e:
    print(f"❌ Training error: {e}")
    # Still try to save checkpoint
    checkpoint_files = [f for f in os.listdir('/content/tts_training/checkpoints') if f.endswith('.pth')]
    if checkpoint_files:
        latest_checkpoint = max(checkpoint_files, key=lambda x: os.path.getmtime(f'/content/tts_training/checkpoints/{x}'))
        save_checkpoint_to_drive(
            f'/content/tts_training/checkpoints/{latest_checkpoint}',
            f'/content/drive/MyDrive/TTS_Checkpoints/{latest_checkpoint}'
        )


# ================================
# 9. INFERENCE AND TESTING
# ================================

def test_trained_model(checkpoint_path, text_to_speak):
    """Test the trained model with custom text - Updated for compatibility"""
    try:
        # Method 1: Try direct TTS API
        from TTS.api import TTS

        # Find config file
        config_file = '/content/tts_training/config.json'
        if not os.path.exists(config_file):
            config_file = None

        # Initialize TTS with trained model
        if config_file:
            tts = TTS(model_path=checkpoint_path, config_path=config_file)
        else:
            tts = TTS(model_path=checkpoint_path)

        # Generate speech
        output_path = '/content/tts_training/test_output.wav'
        tts.tts_to_file(text=text_to_speak, file_path=output_path)

        print(f"✅ Audio generated: {output_path}")
        return output_path

    except Exception as e:
        print(f"❌ Method 1 failed: {e}")

        # Method 2: Try using pre-trained model for testing
        try:
            from TTS.api import TTS

            # Use a pre-trained model that supports similar features
            tts = TTS(model_name="tts_models/en/ljspeech/tacotron2-DDC", progress_bar=False)

            # Generate speech (note: this will be in English voice, just for testing)
            output_path = '/content/tts_training/test_output_pretrained.wav'
            tts.tts_to_file(text=text_to_speak, file_path=output_path)

            print(f"✅ Test audio generated with pre-trained model: {output_path}")
            print("⚠️  Note: This uses a pre-trained English model for testing purposes")
            return output_path

        except Exception as e2:
            print(f"❌ Method 2 failed: {e2}")

            # Method 3: Simple text-to-speech check
            try:
                print("🔧 Checking if model files exist...")
                if os.path.exists(checkpoint_path):
                    print(f"✅ Model checkpoint found: {checkpoint_path}")
                    file_size = os.path.getsize(checkpoint_path)
                    print(f"📁 File size: {file_size / (1024*1024):.2f} MB")

                    # Create a simple test output
                    import numpy as np
                    sample_rate = 22050
                    duration = 2.0
                    t = np.linspace(0, duration, int(sample_rate * duration))

                    # Generate a simple tone as test
                    frequency = 440  # A4 note
                    test_audio = 0.3 * np.sin(2 * np.pi * frequency * t)

                    output_path = '/content/tts_training/test_tone.wav'
                    import soundfile as sf
                    sf.write(output_path, test_audio, sample_rate)

                    print(f"✅ Test tone generated: {output_path}")
                    print("💡 Your model is saved, but inference needs manual setup")
                    return output_path

                else:
                    print(f"❌ Model checkpoint not found: {checkpoint_path}")
                    return None

            except Exception as e3:
                print(f"❌ All methods failed: {e3}")
                return None

# Alternative inference function for manual setup
def setup_inference_manually():
    """
    Setup inference manually if automated methods fail
    """
    print("🔧 Manual inference setup:")
    print("1. Check if training completed successfully")
    print("2. Look for .pth files in /content/tts_training/checkpoints/")
    print("3. Use the following code for inference:")

    inference_code = '''
# Manual inference code
import torch
from TTS.api import TTS

# Load your trained model
model_path = "/content/tts_training/checkpoints/best_model.pth"
config_path = "/content/tts_training/config.json"

# Initialize TTS
tts = TTS(model_path=model_path, config_path=config_path)

# Generate speech
text = "Xin chào, đây là giọng nói tiếng Việt."
output_path = "/content/output.wav"
tts.tts_to_file(text=text, file_path=output_path)

print(f"Audio saved to: {output_path}")
'''

    print(inference_code)

    # Save inference code to file
    with open('/content/manual_inference.py', 'w', encoding='utf-8') as f:
        f.write(inference_code)

    print("💾 Manual inference code saved to: /content/manual_inference.py")

# Test with sample text
test_text = "Xin chào! Đây là mô hình tổng hợp tiếng nói tiếng Việt được huấn luyện từ dữ liệu của bạn."
print(f"🎤 Testing with text: {test_text}")

# Find latest checkpoint
checkpoint_dir = '/content/tts_training/checkpoints'
if os.path.exists(checkpoint_dir):
    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]
    if checkpoint_files:
        latest_checkpoint = max(checkpoint_files, key=lambda x: os.path.getmtime(f'{checkpoint_dir}/{x}'))
        print(f"🔍 Found checkpoint: {latest_checkpoint}")
        test_audio = test_trained_model(f'{checkpoint_dir}/{latest_checkpoint}', test_text)
        if test_audio:
            print("🎧 You can download the generated audio file!")
        else:
            print("🔧 Setting up manual inference...")
            setup_inference_manually()
    else:
        print("❌ No checkpoint files found. Please complete training first.")
else:
    print("❌ Checkpoint directory not found. Please run training first.")

print("\n" + "="*50)
print("🎉 TRAINING PIPELINE SETUP COMPLETE!")